"""
Calculation fixtures with expected results for testing mathematical accuracy
"""

import math

# Funding rate calculation test cases
FUNDING_RATE_CALCULATIONS = {
    "bybit_8h_positive": {
        "input": {
            "funding_rate_8h": 0.0001,
            "exchange": "bybit",
            "trading_cost": 0.1095
        },
        "expected": {
            "annualized_rate": 0.0001 * 3 * 365,  # 0.1095
            "adjusted_rate": 0.0001 * 3 * 365 - 0.1095  # 0.0
        }
    },
    
    "bybit_8h_negative": {
        "input": {
            "funding_rate_8h": -0.0002,
            "exchange": "bybit",
            "trading_cost": 0.1095
        },
        "expected": {
            "annualized_rate": -0.0002 * 3 * 365,  # -0.219
            "adjusted_rate": -0.0002 * 3 * 365 - 0.1095  # -0.3285
        }
    },
    
    "hyperliquid_1h": {
        "input": {
            "funding_rate_1h": 0.00001,
            "exchange": "hyperliquid",
            "trading_cost": 0.0000125
        },
        "expected": {
            "annualized_rate": 0.00001 * 24 * 365,  # 0.0876
            "adjusted_rate": 0.00001 * 24 * 365 - 0.0000125  # 0.0875875
        }
    }
}

# Volatility calculation test cases
VOLATILITY_CALCULATIONS = {
    "simple_returns": {
        "input": {
            "returns": [0.01, -0.02, 0.015, -0.01, 0.005],
            "annualization_factor": 365
        },
        "expected": {
            "volatility": math.sqrt(sum([(r - sum([0.01, -0.02, 0.015, -0.01, 0.005])/5)**2 for r in [0.01, -0.02, 0.015, -0.01, 0.005]]) / 4) * math.sqrt(365)
        }
    },
    
    "zero_volatility": {
        "input": {
            "returns": [0.01, 0.01, 0.01, 0.01, 0.01],
            "annualization_factor": 365
        },
        "expected": {
            "volatility": 0.0
        }
    },
    
    "high_volatility": {
        "input": {
            "returns": [0.1, -0.1, 0.1, -0.1, 0.1],
            "annualization_factor": 365
        },
        "expected": {
            "volatility": math.sqrt(0.04) * math.sqrt(365)  # High volatility case
        }
    }
}

# Position sizing calculation test cases
POSITION_SIZING_CALCULATIONS = {
    "equal_weights": {
        "input": {
            "total_capital": 10000,
            "num_positions": 5,
            "prices": [50000, 3000, 100, 8, 0.5],
            "volatilities": [0.2, 0.25, 0.3, 0.35, 0.4]
        },
        "expected": {
            "equal_weight_sizes": [0.04, 0.667, 20.0, 250.0, 4000.0],  # $2000 each
            "total_notional": 10000
        }
    },
    
    "volatility_targeted": {
        "input": {
            "total_capital": 10000,
            "target_volatility": 0.2,
            "positions": [
                {"symbol": "BTCUSDT", "price": 50000, "volatility": 0.25, "weight": 0.5},
                {"symbol": "ETHUSDT", "price": 3000, "volatility": 0.30, "weight": 0.5}
            ]
        },
        "expected": {
            "leverage_adjustments": [0.8, 0.667],  # target_vol / individual_vol
            "adjusted_weights": [0.545, 0.455],  # Normalized after leverage adjustment
            "position_sizes": [0.109, 1.517]  # Final position sizes
        }
    },
    
    "linear_decay_weighting": {
        "input": {
            "scores": [2.0, 1.0, 0.5, -0.5, -1.0],
            "total_positions": 5
        },
        "expected": {
            "weights": [0.4, 0.3, 0.2, 0.1, 0.0],  # Linear decay from top to bottom
            "normalized_weights": [0.4, 0.3, 0.2, 0.1, 0.0]  # Should sum to 1.0
        }
    }
}

# Portfolio metrics calculation test cases
PORTFOLIO_METRICS_CALCULATIONS = {
    "basic_returns": {
        "input": {
            "returns": [0.01, 0.02, -0.01, 0.015, -0.005],
            "risk_free_rate": 0.02
        },
        "expected": {
            "total_return": 0.0295,  # Cumulative return
            "annualized_return": (1.0295 ** (365/5)) - 1,  # Annualized
            "volatility": math.sqrt(sum([(r - 0.006)**2 for r in [0.01, 0.02, -0.01, 0.015, -0.005]]) / 4) * math.sqrt(365),
            "sharpe_ratio": None,  # Will be calculated based on above
            "max_drawdown": 0.015,  # Maximum peak-to-trough decline
            "calmar_ratio": None   # Will be calculated as annualized_return / max_drawdown
        }
    },
    
    "negative_returns": {
        "input": {
            "returns": [-0.01, -0.02, -0.015, -0.01, -0.005],
            "risk_free_rate": 0.02
        },
        "expected": {
            "total_return": -0.0595,
            "annualized_return": (0.9405 ** (365/5)) - 1,
            "max_drawdown": 0.0595,
            "sharpe_ratio": None,  # Negative Sharpe ratio
            "calmar_ratio": None   # Negative Calmar ratio
        }
    },
    
    "perfect_returns": {
        "input": {
            "returns": [0.01] * 100,  # Consistent positive returns
            "risk_free_rate": 0.02
        },
        "expected": {
            "total_return": (1.01 ** 100) - 1,
            "volatility": 0.0,  # No volatility
            "max_drawdown": 0.0,  # No drawdown
            "sharpe_ratio": float('inf')  # Infinite Sharpe ratio (no volatility)
        }
    }
}

# Beta calculation test cases
BETA_CALCULATIONS = {
    "market_neutral": {
        "input": {
            "asset_returns": [0.01, -0.02, 0.015, -0.01, 0.005],
            "market_returns": [0.02, -0.01, 0.01, -0.015, 0.008],
            "lambda_ridge": 0.01
        },
        "expected": {
            "beta": 0.5,  # Approximate beta
            "r_squared": 0.25  # Approximate R-squared
        }
    },
    
    "high_beta": {
        "input": {
            "asset_returns": [0.02, -0.04, 0.03, -0.02, 0.01],
            "market_returns": [0.01, -0.02, 0.015, -0.01, 0.005],
            "lambda_ridge": 0.01
        },
        "expected": {
            "beta": 2.0,  # High beta asset
            "r_squared": 0.8  # High correlation
        }
    }
}

# Cross-sectional momentum calculation test cases
MOMENTUM_CALCULATIONS = {
    "z_score_calculation": {
        "input": {
            "returns": [0.05, 0.02, -0.01, 0.03, -0.02],
            "lookback_period": 20
        },
        "expected": {
            "mean_return": 0.014,
            "std_return": 0.0265,
            "z_score": (0.05 - 0.014) / 0.0265  # Latest return z-score
        }
    },
    
    "ranking_calculation": {
        "input": {
            "z_scores": [1.5, -0.5, 0.8, -1.2, 0.3],
            "symbols": ["BTC", "ETH", "ADA", "SOL", "DOT"]
        },
        "expected": {
            "rankings": [1, 4, 2, 5, 3],  # Rank by z-score (1 = highest)
            "top_long": ["BTC", "ADA"],   # Top 2 for long positions
            "top_short": ["SOL", "ETH"]   # Bottom 2 for short positions
        }
    }
}

# Trend trading calculation test cases
TREND_CALCULATIONS = {
    "ema_calculation": {
        "input": {
            "prices": [100, 102, 101, 103, 105, 104, 106, 108],
            "short_period": 3,
            "long_period": 5
        },
        "expected": {
            "ema_short": 106.5,  # Approximate EMA(3)
            "ema_long": 104.2,   # Approximate EMA(5)
            "trend_signal": 0.022  # (ema_short - ema_long) / ema_long
        }
    },
    
    "log_price_calculation": {
        "input": {
            "prices": [100, 110, 121, 133.1, 146.41],
            "use_log_prices": True
        },
        "expected": {
            "log_returns": [math.log(110/100), math.log(121/110), math.log(133.1/121), math.log(146.41/133.1)],
            "volatility": math.sqrt(sum([(r - 0.095)**2 for r in [0.095, 0.095, 0.095, 0.095]]) / 3) * math.sqrt(365)
        }
    }
}

# Contract specification validation test cases
CONTRACT_VALIDATION = {
    "valid_btc_position": {
        "input": {
            "symbol": "BTCUSDT",
            "size": 0.001,
            "price": 50000,
            "contract_size": 1.0,
            "min_size": 0.001,
            "max_size": 1000,
            "size_increment": 0.001
        },
        "expected": {
            "is_valid": True,
            "notional_usd": 50.0,
            "rounded_size": 0.001
        }
    },
    
    "invalid_size_too_small": {
        "input": {
            "symbol": "BTCUSDT",
            "size": 0.0005,
            "price": 50000,
            "min_size": 0.001
        },
        "expected": {
            "is_valid": False,
            "error": "Position size below minimum"
        }
    },
    
    "size_rounding": {
        "input": {
            "symbol": "ETHUSDT",
            "size": 0.0123456,
            "size_increment": 0.01
        },
        "expected": {
            "rounded_size": 0.01,
            "is_valid": True
        }
    }
}
