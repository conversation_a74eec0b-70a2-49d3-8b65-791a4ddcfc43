"""
Integration tests for data flow pipeline
"""

import pytest
import asyncio
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from tests.helpers.mock_exchanges import <PERSON>ckExchange
from tests.fixtures.config_fixtures import MINIMAL_CONFIG


@pytest.mark.asyncio
@pytest.mark.integration
class TestDataFlowIntegration:
    """Test data flow integration"""
    
    @pytest.fixture
    async def mock_exchange(self):
        """Create and initialize mock exchange"""
        exchange = MockExchange()
        await exchange.initialize(MINIMAL_CONFIG)
        return exchange
    
    async def test_basic_data_flow(self, mock_exchange):
        """Test basic data flow works"""
        # Test ticker fetching
        ticker = await mock_exchange.fetch_ticker("BTCUSDT")
        assert ticker is not None
        assert ticker['symbol'] == "BTCUSDT"
        
        # Test OHLCV fetching
        ohlcv = await mock_exchange.fetch_ohlcv("BTCUSDT", "1h", 10)
        assert ohlcv is not None
        assert len(ohlcv) == 10
    
    async def test_multiple_symbol_data_flow(self, mock_exchange):
        """Test data flow with multiple symbols"""
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        
        for symbol in symbols:
            ticker = await mock_exchange.fetch_ticker(symbol)
            assert ticker is not None
            assert ticker['symbol'] == symbol
    
    async def test_concurrent_data_flow(self, mock_exchange):
        """Test concurrent data fetching"""
        symbols = ["BTCUSDT", "ETHUSDT"]
        
        tasks = [mock_exchange.fetch_ticker(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks)
        
        assert len(results) == len(symbols)
        for i, result in enumerate(results):
            assert result['symbol'] == symbols[i]


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
