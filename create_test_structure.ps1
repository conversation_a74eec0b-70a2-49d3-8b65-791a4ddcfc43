# PowerShell script to create comprehensive test directory structure

$directories = @(
    # Unit tests
    "tests\unit\config",
    "tests\unit\core", 
    "tests\unit\data",
    "tests\unit\exchanges",
    "tests\unit\execution",
    "tests\unit\portfolio",
    "tests\unit\storage",
    "tests\unit\strategies",
    "tests\unit\utils",
    
    # Integration tests
    "tests\integration\data_flow",
    "tests\integration\strategy_execution",
    "tests\integration\portfolio_combination",
    "tests\integration\order_execution",
    
    # End-to-end tests
    "tests\end_to_end\full_system",
    "tests\end_to_end\strategy_workflows",
    "tests\end_to_end\multi_strategy",
    
    # Real API tests
    "tests\real_api\bybit",
    "tests\real_api\binance", 
    "tests\real_api\okx",
    "tests\real_api\hyperliquid",
    
    # Validation tests
    "tests\validation\calculations",
    "tests\validation\units",
    "tests\validation\data_integrity",
    
    # Fixtures and helpers
    "tests\fixtures\market_data",
    "tests\fixtures\config_files",
    "tests\fixtures\expected_results",
    "tests\helpers"
)

Write-Host "Creating comprehensive test directory structure..."

foreach ($dir in $directories) {
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
    Write-Host "Created: $dir"
}

Write-Host "Test directory structure created successfully!"
