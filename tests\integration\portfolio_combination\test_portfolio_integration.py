"""
Integration tests for portfolio combination
"""

import pytest
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from tests.fixtures.config_fixtures import MINIMAL_CONFIG


@pytest.mark.integration
class TestPortfolioCombinationIntegration:
    """Test portfolio combination integration"""
    
    def test_basic_portfolio_combination(self):
        """Test basic portfolio combination logic"""
        # Simple test for portfolio combination
        positions = [
            {"symbol": "BTCUSDT", "side": "long", "size": 0.1, "price": 50000.0},
            {"symbol": "ETHUSDT", "side": "short", "size": -1.0, "price": 3000.0}
        ]
        
        # Test that positions have required fields
        for position in positions:
            assert "symbol" in position
            assert "side" in position
            assert "size" in position
            assert "price" in position
    
    def test_position_netting(self):
        """Test position netting logic"""
        # Test positions that should be netted
        positions = [
            {"symbol": "BTCUSDT", "side": "long", "size": 0.1, "price": 50000.0},
            {"symbol": "BTCUSDT", "side": "long", "size": 0.05, "price": 51000.0}
        ]
        
        # Should be able to combine positions for same symbol
        symbols = [pos["symbol"] for pos in positions]
        unique_symbols = set(symbols)
        
        # Multiple positions for same symbol should be combinable
        assert len(symbols) >= len(unique_symbols)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
