#!/usr/bin/env python3

"""
Beta Calculator Module

Calculates rolling beta for crypto assets against a market index (typically BTC).
Beta measures the sensitivity of an asset's returns to market returns.

Key Features:
- 90-day rolling beta calculation using ridge regression
- Ridge regression with lambda=0.01 for numerical stability
- Log returns for cleaner mathematics
- Daily updates with caching for performance optimization
- Robust error handling for missing data
- Market index data fetching and caching
"""

import asyncio
from typing import Dict, List, Optional, Any
import numpy as np
from sklearn.linear_model import Ridge

from utils import get_logger
# Cache key will be generated inline

logger = get_logger(__name__)


class BetaCalculator:
    """
    Calculate rolling beta for crypto assets against a market index using ridge regression.

    Beta is calculated using ridge regression: asset_returns = α + β * market_returns + ε
    Ridge regularization (lambda=0.01) provides numerical stability and reduces overfitting.

    A beta of:
    - 1.0 means the asset moves with the market
    - > 1.0 means the asset is more volatile than the market
    - < 1.0 means the asset is less volatile than the market
    - 0.0 means no correlation with the market
    """
    
    def __init__(self, data_fetcher, config: Dict[str, Any]):
        self.data_fetcher = data_fetcher
        self.config = config
        self.market_index_symbol = config.get('market_index_symbol', 'BTCUSDT')
        self.beta_calculation_days = config.get('beta_calculation_days', 90)  # Changed to 90 days
        self.default_beta = config.get('default_beta', 1.0)
        self.ridge_lambda = config.get('ridge_lambda', 0.01)  # Ridge regression lambda parameter

        logger.info(f"🔧 Beta Calculator initialized with market index: {self.market_index_symbol}")
        logger.info(f"🔧 Beta calculation period: {self.beta_calculation_days} days")
        logger.info(f"🔧 Ridge regression lambda: {self.ridge_lambda}")
    
    async def calculate_beta(self, symbol: str) -> Optional[float]:
        """
        Calculate 90-day rolling beta for a symbol against the market index using ridge regression.

        Uses log returns and ridge regression with lambda=0.01 for numerical stability.
        Updated daily with caching for performance.

        Args:
            symbol: Trading symbol (e.g., 'ETHUSDT')

        Returns:
            Beta value or None if calculation fails
        """
        try:
            # Check cache first
            cache_key = f"beta:{symbol}:{self.beta_calculation_days}d"
            cached_beta = self.data_fetcher.cache.get(cache_key)
            if cached_beta is not None:
                logger.debug(f"📋 Using cached beta for {symbol}: {cached_beta:.4f}")
                return cached_beta
            
            # Skip beta calculation for the market index itself
            if symbol == self.market_index_symbol:
                beta = 1.0  # Market index has beta of 1.0 by definition
                self.data_fetcher.cache.set(cache_key, beta, ttl=self.config.get('beta_cache_ttl', 3600))
                logger.debug(f"📊 {symbol} is market index, beta = 1.0")
                return beta
            
            # Fetch OHLCV data for both asset and market index
            asset_data, market_data = await asyncio.gather(
                self.data_fetcher.get_cached_ohlcv(symbol, '1d', self.beta_calculation_days + 1),
                self.data_fetcher.get_cached_ohlcv(self.market_index_symbol, '1d', self.beta_calculation_days + 1),
                return_exceptions=True
            )
            
            # Handle fetch errors
            if isinstance(asset_data, Exception):
                logger.warning(f"⚠️ Failed to fetch asset data for {symbol}: {asset_data}")
                return None
            if isinstance(market_data, Exception):
                logger.warning(f"⚠️ Failed to fetch market data for {self.market_index_symbol}: {market_data}")
                return None
            
            # Validate data availability
            if not asset_data or len(asset_data) < self.beta_calculation_days + 1:
                logger.debug(f"⚠️ Insufficient asset data for {symbol} beta calculation")
                return None
            if not market_data or len(market_data) < self.beta_calculation_days + 1:
                logger.debug(f"⚠️ Insufficient market data for {self.market_index_symbol} beta calculation")
                return None
            
            # Calculate returns for both asset and market
            asset_returns = self._calculate_returns(asset_data)
            market_returns = self._calculate_returns(market_data)
            
            if not asset_returns or not market_returns:
                logger.debug(f"⚠️ Failed to calculate returns for {symbol} beta calculation")
                return None
            
            # Ensure we have the same number of returns (handle potential data misalignment)
            min_length = min(len(asset_returns), len(market_returns))
            if min_length < self.beta_calculation_days:
                logger.debug(f"⚠️ Insufficient aligned returns for {symbol} beta calculation ({min_length} < {self.beta_calculation_days})")
                return None
            
            # Take the last N returns to ensure alignment
            asset_returns = asset_returns[-min_length:]
            market_returns = market_returns[-min_length:]
            
            # Calculate beta using numpy for numerical stability
            beta = self._calculate_beta_from_returns(asset_returns, market_returns)
            
            if beta is None:
                logger.debug(f"⚠️ Beta calculation failed for {symbol}")
                return None
            
            # Cache the result
            cache_ttl = self.config.get('beta_cache_ttl', 3600)
            self.data_fetcher.cache.set(cache_key, beta, ttl=cache_ttl)
            
            logger.debug(f"📊 {symbol} beta: {beta:.4f} (vs {self.market_index_symbol})")
            return beta
            
        except Exception as e:
            logger.error(f"❌ Error calculating beta for {symbol}: {e}")
            return None
    
    def _calculate_returns(self, ohlcv_data: List[List]) -> Optional[List[float]]:
        """
        Calculate daily log returns from OHLCV data.

        Args:
            ohlcv_data: OHLCV data [[timestamp, open, high, low, close, volume], ...]

        Returns:
            List of daily log returns or None if calculation fails
        """
        try:
            if len(ohlcv_data) < 2:
                return None

            # Extract closing prices
            closes = [float(candle[4]) for candle in ohlcv_data]

            # Calculate daily log returns with division by zero protection
            returns = []
            for i in range(1, len(closes)):
                if closes[i-1] <= 0 or closes[i] <= 0:
                    logger.warning(f"⚠️ Zero or negative price detected in beta calculation, skipping")
                    continue
                # Log return: ln(P_t / P_{t-1}) = ln(P_t) - ln(P_{t-1})
                log_return = np.log(closes[i]) - np.log(closes[i-1])
                returns.append(log_return)

            return returns if len(returns) >= self.beta_calculation_days else None

        except Exception as e:
            logger.debug(f"❌ Error calculating log returns: {e}")
            return None
    
    def _calculate_beta_from_returns(self, asset_returns: List[float], market_returns: List[float]) -> Optional[float]:
        """
        Calculate beta from asset and market log returns using ridge regression.

        Ridge regression: y = α + β*x + ε, where y = asset returns, x = market returns
        Ridge regularization helps with numerical stability and overfitting.

        Args:
            asset_returns: List of asset daily log returns
            market_returns: List of market daily log returns

        Returns:
            Beta value or None if calculation fails
        """
        try:
            if len(asset_returns) != len(market_returns) or len(asset_returns) < 2:
                return None

            # Convert to numpy arrays
            asset_array = np.array(asset_returns).reshape(-1, 1)  # y (dependent variable)
            market_array = np.array(market_returns).reshape(-1, 1)  # X (independent variable)

            # Check for NaN or infinite values
            if np.any(np.isnan(asset_array)) or np.any(np.isnan(market_array)):
                logger.debug(f"⚠️ NaN values detected in returns, using default beta")
                return self.default_beta

            if np.any(np.isinf(asset_array)) or np.any(np.isinf(market_array)):
                logger.debug(f"⚠️ Infinite values detected in returns, using default beta")
                return self.default_beta

            # Check for zero variance in market returns
            if np.var(market_array) == 0:
                logger.debug(f"⚠️ Market variance is zero, cannot calculate beta")
                return self.default_beta

            # Fit ridge regression: asset_returns = alpha + beta * market_returns
            ridge_model = Ridge(alpha=self.ridge_lambda, fit_intercept=True)
            ridge_model.fit(market_array, asset_array.ravel())

            # Extract beta coefficient (slope)
            beta = ridge_model.coef_[0]

            # Handle NaN or infinite beta
            if np.isnan(beta) or np.isinf(beta):
                logger.debug(f"⚠️ Ridge regression beta is NaN or infinite, using default beta")
                return self.default_beta

            # Sanity check: extremely high or low beta values might indicate data issues
            if abs(beta) > 10:
                logger.warning(f"⚠️ Extreme beta value detected: {beta:.4f}, capping at ±10")
                beta = 10.0 if beta > 0 else -10.0

            return float(beta)

        except Exception as e:
            logger.debug(f"❌ Error calculating beta from returns using ridge regression: {e}")
            return None
    
    async def calculate_portfolio_beta(self, positions: List[Dict]) -> float:
        """
        Calculate portfolio-level beta as weighted average of individual asset betas.
        
        Portfolio Beta = Σ(weight_i × beta_i)
        
        Args:
            positions: List of position dictionaries with 'symbol' and 'weight' keys
            
        Returns:
            Portfolio beta value
        """
        try:
            if not positions:
                return 0.0
            
            total_weighted_beta = 0.0
            total_weight = 0.0
            
            for position in positions:
                symbol = position.get('symbol')
                weight = position.get('weight', 0.0)
                
                if not symbol or weight == 0:
                    continue
                
                # Get beta for this asset
                beta = await self.calculate_beta(symbol)
                if beta is None:
                    beta = self.default_beta
                    logger.debug(f"⚠️ Using default beta {beta} for {symbol}")
                
                # Add to weighted sum
                total_weighted_beta += weight * beta
                total_weight += abs(weight)  # Use absolute weight for proper normalization
                
                logger.debug(f"📊 {symbol}: weight={weight:.4f}, beta={beta:.4f}, contribution={weight*beta:.4f}")
            
            # Calculate portfolio beta
            if total_weight == 0:
                portfolio_beta = 0.0
            else:
                portfolio_beta = total_weighted_beta / total_weight
            
            logger.info(f"📊 Portfolio beta: {portfolio_beta:.4f} (total weight: {total_weight:.4f})")
            return portfolio_beta
            
        except Exception as e:
            logger.error(f"❌ Error calculating portfolio beta: {e}")
            return 0.0


# Cache keys are now generated inline
