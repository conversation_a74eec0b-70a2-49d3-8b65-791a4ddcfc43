"""
Simple data caching with TTL and LRU eviction
"""

import time
import threading
import logging
from typing import Any, Optional, Tuple
from collections import OrderedDict

logger = logging.getLogger(__name__)


class DataCache:
    """Simple TTL-based cache with LRU eviction"""

    def __init__(self, default_ttl: int = 300, max_size: int = 1000):
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.cache: OrderedDict[str, Tuple[Any, float, float]] = OrderedDict()  # key -> (data, timestamp, ttl)
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0
        }
        self._lock = threading.RLock()

    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        with self._lock:
            if key not in self.cache:
                self.stats['misses'] += 1
                return None

            data, timestamp, ttl = self.cache[key]
            current_time = time.time()

            # Check if expired
            if current_time - timestamp > ttl:
                del self.cache[key]
                self.stats['misses'] += 1
                return None

            # Move to end (LRU)
            self.cache.move_to_end(key)
            self.stats['hits'] += 1
            return data

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set item in cache"""
        with self._lock:
            if ttl is None:
                ttl = self.default_ttl

            # Remove oldest items if at capacity
            while len(self.cache) >= self.max_size:
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
                self.stats['evictions'] += 1

            # Store new item
            self.cache[key] = (value, time.time(), ttl)
            self.cache.move_to_end(key)

    def delete(self, key: str) -> bool:
        """Delete item from cache"""
        with self._lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False

    def clear(self) -> None:
        """Clear all items from cache"""
        with self._lock:
            self.cache.clear()

    def cleanup_expired(self) -> int:
        """Remove expired items and return count removed"""
        with self._lock:
            current_time = time.time()
            expired_keys = []

            for key, (data, timestamp, ttl) in self.cache.items():
                if current_time - timestamp > ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                del self.cache[key]

            return len(expired_keys)

    def get_stats(self) -> dict:
        """Get cache statistics"""
        with self._lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0

            return {
                **self.stats,
                'size': len(self.cache),
                'hit_rate_percent': hit_rate,
                'total_requests': total_requests
            }

    def size(self) -> int:
        """Get current cache size"""
        return len(self.cache)


# Global cache instance
_global_cache: Optional[DataCache] = None


def get_cache() -> DataCache:
    """Get global cache instance"""
    global _global_cache
    if _global_cache is None:
        _global_cache = DataCache()
    return _global_cache


def configure_cache(default_ttl: int = 300, max_size: int = 1000) -> DataCache:
    """Configure global cache"""
    global _global_cache
    _global_cache = DataCache(default_ttl, max_size)
    logger.info(f"🔧 Cache configured: TTL={default_ttl}s, Max Size={max_size}")
    return _global_cache


# Cache key generators for different data types
class CacheKeys:
    """Cache key generators"""
    
    @staticmethod
    def market_data(exchange: str, symbol: str, data_type: str, timeframe: str = None) -> str:
        """Generate cache key for market data"""
        key_parts = [f"market", exchange, symbol, data_type]
        if timeframe:
            key_parts.append(timeframe)
        return ":".join(key_parts)
    
    @staticmethod
    def funding_rate(exchange: str, symbol: str) -> str:
        """Generate cache key for funding rates"""
        return f"funding:{exchange}:{symbol}"
    
    @staticmethod
    def ticker(exchange: str, symbol: str) -> str:
        """Generate cache key for ticker data"""
        return f"ticker:{exchange}:{symbol}"
    
    @staticmethod
    def orderbook(exchange: str, symbol: str) -> str:
        """Generate cache key for orderbook data"""
        return f"orderbook:{exchange}:{symbol}"
    
    @staticmethod
    def strategy_result(strategy_name: str, exchange: str, timestamp: str) -> str:
        """Generate cache key for strategy results"""
        return f"strategy:{strategy_name}:{exchange}:{timestamp}"
    
    @staticmethod
    def portfolio_metrics(period: str) -> str:
        """Generate cache key for portfolio metrics"""
        return f"portfolio:metrics:{period}"
