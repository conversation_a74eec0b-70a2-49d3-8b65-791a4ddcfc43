"""
Real API tests for Bybit exchange using demo/testnet
"""

import pytest
import asyncio
import os
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from tests.helpers.real_api_helper import RealAPITestHelper
from tests.helpers.assertion_helpers import assert_data_freshness, assert_units_consistent


@pytest.mark.asyncio
@pytest.mark.real_api
class TestBybitRealAPI:
    """Test Bybit exchange with real API using demo/testnet"""
    
    @pytest.fixture
    async def api_helper(self):
        """Create real API test helper"""
        helper = RealAPITestHelper('bybit', use_demo=True)
        
        # Setup and verify connection
        setup_success = await helper.setup()
        if not setup_success:
            pytest.skip("Cannot connect to Bybit demo API - check credentials")
        
        yield helper
        
        # Cleanup
        await helper.cleanup()
    
    async def test_bybit_connectivity(self, api_helper):
        """Test basic Bybit connectivity"""
        success = await api_helper.test_basic_connectivity()
        assert success, "Failed to connect to Bybit demo API"
    
    async def test_bybit_market_data(self, api_helper):
        """Test Bybit market data fetching"""
        symbols = ["BTCUSDT", "ETHUSDT"]
        success = await api_helper.test_market_data_fetching(symbols)
        assert success, "Failed to fetch market data from Bybit"
    
    async def test_bybit_orderbook(self, api_helper):
        """Test Bybit orderbook fetching"""
        symbols = ["BTCUSDT"]
        success = await api_helper.test_orderbook_fetching(symbols)
        assert success, "Failed to fetch orderbook from Bybit"
    
    async def test_bybit_rate_limiting(self, api_helper):
        """Test Bybit rate limiting"""
        success = await api_helper.test_rate_limiting()
        assert success, "Bybit rate limiting test failed"
    
    async def test_bybit_error_handling(self, api_helper):
        """Test Bybit error handling"""
        success = await api_helper.test_error_handling()
        assert success, "Bybit error handling test failed"
    
    async def test_bybit_ticker_data_quality(self, api_helper):
        """Test quality of Bybit ticker data"""
        exchange = api_helper.exchange
        
        ticker = await exchange.fetch_ticker("BTCUSDT")
        
        # Validate ticker structure
        required_fields = ['symbol', 'last', 'bid', 'ask', 'high', 'low', 'baseVolume', 'timestamp']
        for field in required_fields:
            assert field in ticker, f"Missing required field: {field}"
        
        # Validate data quality
        assert ticker['symbol'] == "BTCUSDT"
        assert ticker['last'] > 0, "Last price should be positive"
        assert ticker['bid'] > 0, "Bid price should be positive"
        assert ticker['ask'] > 0, "Ask price should be positive"
        assert ticker['ask'] > ticker['bid'], "Ask should be higher than bid"
        assert ticker['high'] >= ticker['last'], "High should be >= last"
        assert ticker['low'] <= ticker['last'], "Low should be <= last"
        assert ticker['baseVolume'] >= 0, "Volume should be non-negative"
        
        # Check data freshness (within last hour)
        assert_data_freshness(ticker['timestamp'], max_age_seconds=3600)
        
        # Check unit consistency
        assert_units_consistent(ticker['last'], "usd", "ticker last price")
    
    async def test_bybit_ohlcv_data_quality(self, api_helper):
        """Test quality of Bybit OHLCV data"""
        exchange = api_helper.exchange
        
        ohlcv = await exchange.fetch_ohlcv("BTCUSDT", "1h", 24)
        
        assert len(ohlcv) > 0, "Should receive OHLCV data"
        assert len(ohlcv) <= 24, "Should not exceed requested limit"
        
        for i, candle in enumerate(ohlcv):
            assert len(candle) >= 6, f"Candle {i} should have at least 6 elements"
            
            timestamp, open_price, high, low, close, volume = candle[:6]
            
            # Validate data types
            assert isinstance(timestamp, (int, float)), f"Timestamp should be numeric in candle {i}"
            assert isinstance(open_price, (int, float)), f"Open price should be numeric in candle {i}"
            assert isinstance(high, (int, float)), f"High price should be numeric in candle {i}"
            assert isinstance(low, (int, float)), f"Low price should be numeric in candle {i}"
            assert isinstance(close, (int, float)), f"Close price should be numeric in candle {i}"
            assert isinstance(volume, (int, float)), f"Volume should be numeric in candle {i}"
            
            # Validate price relationships
            assert high >= max(open_price, close), f"High >= max(open, close) failed in candle {i}"
            assert low <= min(open_price, close), f"Low <= min(open, close) failed in candle {i}"
            assert all(price > 0 for price in [open_price, high, low, close]), f"All prices should be positive in candle {i}"
            assert volume >= 0, f"Volume should be non-negative in candle {i}"
            
            # Check data freshness for recent candles
            if i >= len(ohlcv) - 3:  # Last 3 candles
                assert_data_freshness(timestamp, max_age_seconds=7200)  # Within 2 hours
        
        # Check chronological order
        timestamps = [candle[0] for candle in ohlcv]
        assert timestamps == sorted(timestamps), "OHLCV data should be in chronological order"
    
    async def test_bybit_funding_rate_data_quality(self, api_helper):
        """Test quality of Bybit funding rate data"""
        exchange = api_helper.exchange
        
        try:
            funding = await exchange.fetch_funding_rate("BTCUSDT")
            
            # Validate funding rate structure
            required_fields = ['symbol', 'fundingRate', 'fundingTimestamp']
            for field in required_fields:
                assert field in funding, f"Missing required field: {field}"
            
            # Validate data quality
            assert funding['symbol'] == "BTCUSDT"
            assert isinstance(funding['fundingRate'], (int, float)), "Funding rate should be numeric"
            assert isinstance(funding['fundingTimestamp'], (int, float)), "Funding timestamp should be numeric"
            
            # Funding rates should be reasonable (typically small)
            assert abs(funding['fundingRate']) < 0.01, "Funding rate should be reasonable (< 1%)"
            
            # Check unit consistency
            assert_units_consistent(funding['fundingRate'], "rate", "funding rate")
            
            # Check data freshness (funding rates update every 8 hours)
            assert_data_freshness(funding['fundingTimestamp'], max_age_seconds=28800)  # 8 hours
            
        except Exception as e:
            pytest.skip(f"Funding rate not available for BTCUSDT: {e}")
    
    async def test_bybit_multiple_symbols(self, api_helper):
        """Test Bybit with multiple symbols"""
        exchange = api_helper.exchange
        
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        
        # Test fetching tickers for multiple symbols
        for symbol in symbols:
            ticker = await exchange.fetch_ticker(symbol)
            assert ticker['symbol'] == symbol
            assert ticker['last'] > 0
        
        # Test concurrent fetching
        tasks = [exchange.fetch_ticker(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                pytest.fail(f"Failed to fetch ticker for {symbols[i]}: {result}")
            else:
                assert result['symbol'] == symbols[i]
    
    async def test_bybit_api_limits_respect(self, api_helper):
        """Test that Bybit API limits are respected"""
        exchange = api_helper.exchange
        
        # Make multiple rapid requests to test rate limiting
        start_time = asyncio.get_event_loop().time()
        
        tasks = []
        for _ in range(10):
            tasks.append(exchange.fetch_ticker("BTCUSDT"))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = asyncio.get_event_loop().time()
        total_time = end_time - start_time
        
        # Should take some time due to rate limiting
        assert total_time > 1.0, "Rate limiting should slow down rapid requests"
        
        # All requests should succeed (no rate limit errors)
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # Allow some failures due to rate limiting, but not all
                pass
            else:
                assert result['symbol'] == "BTCUSDT"
        
        # At least half should succeed
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) >= 5, "At least half of requests should succeed"
    
    @pytest.mark.slow
    async def test_bybit_extended_data_fetch(self, api_helper):
        """Extended test for Bybit data fetching (marked as slow)"""
        exchange = api_helper.exchange
        
        # Test fetching larger amounts of data
        ohlcv = await exchange.fetch_ohlcv("BTCUSDT", "1h", 100)
        
        assert len(ohlcv) > 50, "Should receive substantial OHLCV data"
        
        # Validate all candles
        for candle in ohlcv:
            timestamp, open_price, high, low, close, volume = candle[:6]
            assert high >= max(open_price, close)
            assert low <= min(open_price, close)
            assert all(price > 0 for price in [open_price, high, low, close])
    
    async def test_bybit_demo_mode_verification(self, api_helper):
        """Verify that we're actually using Bybit demo mode"""
        exchange = api_helper.exchange
        
        # Check that we're using demo URLs
        if hasattr(exchange.exchange, 'urls'):
            urls = exchange.exchange.urls
            if 'api' in urls:
                api_urls = urls['api']
                if isinstance(api_urls, dict):
                    for url in api_urls.values():
                        if isinstance(url, str) and 'bybit' in url:
                            # Should contain 'demo' for demo mode
                            assert 'demo' in url or 'testnet' in url, f"Should be using demo/testnet URL: {url}"


@pytest.mark.asyncio
@pytest.mark.integration
class TestBybitIntegrationWithSystem:
    """Integration tests for Bybit with trading system components"""
    
    @pytest.fixture
    async def api_helper(self):
        """Create real API test helper"""
        helper = RealAPITestHelper('bybit', use_demo=True)
        setup_success = await helper.setup()
        if not setup_success:
            pytest.skip("Cannot connect to Bybit demo API")
        yield helper
        await helper.cleanup()
    
    async def test_bybit_with_data_fetcher(self, api_helper):
        """Test Bybit integration with DataFetcher"""
        from data.fetcher import DataFetcher
        from data.cache import DataCache
        from utils.monitoring import PerformanceMonitor
        
        exchange = api_helper.exchange
        config = api_helper.config
        
        # Create data fetcher with real exchange
        cache = DataCache(default_ttl=300)
        monitor = PerformanceMonitor()
        fetcher = DataFetcher(exchange, config, cache, monitor)
        
        # Test data fetching
        ohlcv = await fetcher.fetch_ohlcv_data("BTCUSDT", "1h", 10)
        assert ohlcv is not None
        assert len(ohlcv) > 0
        
        ticker = await fetcher.fetch_ticker_data("BTCUSDT")
        assert ticker is not None
        assert ticker['symbol'] == "BTCUSDT"
    
    async def test_bybit_with_data_analyzer(self, api_helper):
        """Test Bybit integration with DataAnalyzer"""
        from data.analyzer import DataAnalyzer
        
        exchange = api_helper.exchange
        config = api_helper.config
        
        # Create data analyzer with real exchange
        analyzer = DataAnalyzer(exchange, config)
        
        # Fetch real data
        ohlcv = await exchange.fetch_ohlcv("BTCUSDT", "1h", 50)
        
        # Test analysis functions
        volatility = analyzer.calculate_volatility_from_ohlcv(ohlcv)
        assert volatility is not None
        assert volatility > 0
        
        returns = analyzer.calculate_returns_from_ohlcv(ohlcv)
        assert returns is not None
        assert len(returns) > 0


if __name__ == '__main__':
    # Run only Bybit real API tests
    pytest.main([__file__, '-v', '-m', 'real_api'])
