"""
Statistical Arbitrage Carry Trade Strategy Implementation

This strategy implements funding arbitrage by:
1. Selecting universe based on volume and volatility filters (no funding rate bounds)
2. Calculating annualized funding rates with fixed 10.95% trading cost adjustment
3. Using top 5 selection (most negative for longs, most positive for shorts)
4. Applying linear decay weighting + individual volatility targeting + contract compliance
"""

import logging
from typing import Dict, List, Any, Tuple
import yaml
from pathlib import Path

from strategies.base import BaseStrategy, StrategyPosition
from utils.data_validation import DataUnitValidator

logger = logging.getLogger(__name__)


class StatArbCarryTradeStrategy(BaseStrategy):
    """
    Statistical Arbitrage Carry Trade Strategy

    Implements funding arbitrage using top 5 selection and
    linear decay weighting with individual volatility targeting (beta projection disabled).
    """
    
    def __init__(self, data_fetcher, data_analyzer, exchange, main_config: Dict[str, Any]):
        """
        Initialize the strategy with shared resources
        
        Args:
            data_fetcher: Shared data fetcher instance
            data_analyzer: Shared data analyzer instance
            exchange: Shared exchange interface
            main_config: Main configuration dictionary
        """
        # Load strategy-specific configuration
        strategy_config = self._load_strategy_config()
        
        # Initialize base strategy
        super().__init__(
            strategy_name="stat_arb_carry_trade",
            config=strategy_config,
            data_fetcher=data_fetcher,
            data_analyzer=data_analyzer,
            exchange=exchange
        )
        
        # Store main config for shared parameters
        self.main_config = main_config
        
        # Extract frequently used parameters
        self.total_capital = main_config.get('total_capital_usd', 10000)
        self.exchange_name = main_config.get('exchange', 'bybit')
        
        self.logger.info(f"🏗️ StatArb Carry Trade strategy initialized")
        self.logger.info(f"   Total capital: ${self.total_capital:,.0f}")
        self.logger.info(f"   Exchange: {self.exchange_name}")
    
    def _load_strategy_config(self) -> Dict[str, Any]:
        """Load strategy-specific configuration from YAML file"""
        try:
            config_path = Path(__file__).parent / "config.yaml"
            
            if not config_path.exists():
                raise FileNotFoundError(f"Strategy config file not found: {config_path}")
            
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            logger.info(f"📂 Loaded strategy config from {config_path}")
            return config
            
        except Exception as e:
            logger.error(f"❌ Failed to load strategy config: {e}")
            raise
    
    def _validate_config(self) -> None:
        """Validate strategy-specific configuration parameters"""
        required_params = [
            'min_daily_volume_usd',
            'peak_abs_funding_rate', 
            'max_position_capital_pct',
            'target_volatility',
            'min_positions_per_leg'
        ]
        
        for param in required_params:
            if param not in self.config:
                raise ValueError(f"Missing required strategy parameter: {param}")
        
        # Validate parameter ranges
        if self.config['peak_abs_funding_rate'] <= 0:
            raise ValueError("peak_abs_funding_rate must be positive")
        
        if not 0 < self.config['max_position_capital_pct'] <= 100:
            raise ValueError("max_position_capital_pct must be between 0 and 100")
        
        if self.config['target_volatility'] <= 0:
            raise ValueError("target_volatility must be positive")
        
        if self.config['min_positions_per_leg'] < 1:
            raise ValueError("min_positions_per_leg must be at least 1")
        
        self.logger.info("✅ Strategy configuration validated")
    
    async def get_universe(self) -> List[str]:
        """
        Get the universe of symbols for funding arbitrage

        Applies strategy-specific filters:
        - 90 days minimum listing age (increased from 60 days)
        - 90 days minimum historical data (increased from 60 days)
        - Volume and volatility filters
        - Exclusion of stablecoins and wrapped coins

        Returns:
            List of eligible symbol strings
        """
        try:
            self.logger.info("🌍 Fetching universe for funding arbitrage strategy...")
            self.logger.info(f"📊 Strategy-specific filters: {self.config.get('exclude_new_listings_days', 90)} days min listing age, "
                           f"{self.config.get('min_historical_data_days', 90)} days min historical data")

            # Create a temporary data fetcher with strategy-specific config
            # This ensures the stat arb strategy uses its own 90-day requirements
            strategy_config = self.data_fetcher.config.copy()
            strategy_config.update({
                'exclude_new_listings_days': self.config.get('exclude_new_listings_days', 90),
                'min_historical_data_days': self.config.get('min_historical_data_days', 90),
                'min_daily_volume_usd': self.config.get('min_daily_volume_usd', 1000000),
                'min_volatility_threshold': self.config.get('min_volatility_threshold', 0.05),
                'exclude_stablecoins': self.config.get('exclude_stablecoins', True),
                'exclude_wrapped_coins': self.config.get('exclude_wrapped_coins', True)
            })

            # Temporarily update the data fetcher config
            original_config = self.data_fetcher.config
            self.data_fetcher.config = strategy_config

            try:
                # Get eligible coins using the strategy-specific configuration
                eligible_coins = await self.data_fetcher.get_eligible_coins()
            finally:
                # Restore original config
                self.data_fetcher.config = original_config

            if not eligible_coins:
                self.logger.warning("⚠️ No eligible coins found from data fetcher")
                return []

            # Extract symbols from eligible coins
            universe = [coin['symbol'] for coin in eligible_coins]

            self.logger.info(f"✅ Strategy universe contains {len(universe)} symbols")
            self.logger.info(f"📊 Applied 90-day listing age and historical data requirements")

            # Log sample of universe for debugging
            if self.config.get('log_position_selection_details', False):
                sample_size = min(10, len(universe))
                sample_symbols = universe[:sample_size]
                self.logger.debug(f"📊 Universe sample: {sample_symbols}")

            return universe

        except Exception as e:
            self.logger.error(f"❌ Failed to get strategy universe: {e}")
            raise
    
    async def calculate_features(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """
        Calculate funding arbitrage features for symbols
        
        Features include:
        - Funding rates and cost adjustments
        - Volatility metrics
        - Beta values (if enabled)
        - Volume metrics
        
        Args:
            symbols: List of symbols to calculate features for
            
        Returns:
            List of enriched symbol dictionaries with calculated features
        """
        try:
            self.logger.info(f"📊 Calculating features for {len(symbols)} symbols...")
            
            # Get eligible coins data (includes funding rates, volume, etc.)
            eligible_coins = await self.data_fetcher.get_eligible_coins()
            
            # Filter to only requested symbols
            symbol_set = set(symbols)
            filtered_coins = [
                coin for coin in eligible_coins 
                if coin['symbol'] in symbol_set
            ]
            
            if not filtered_coins:
                self.logger.warning("⚠️ No coins found matching requested symbols")
                return []
            
            self.logger.info(f"📊 Found data for {len(filtered_coins)} symbols")
            
            # Enrich with volatility and beta data using shared analyzer
            enriched_coins = await self.data_analyzer.enrich_coins_with_volatility(filtered_coins)
            
            if not enriched_coins:
                self.logger.warning("⚠️ No symbols passed volatility enrichment")
                return []
            
            self.logger.info(f"✅ Successfully calculated features for {len(enriched_coins)} symbols")
            
            # Log feature calculation details if enabled
            if self.config.get('log_position_selection_details', False):
                for coin in enriched_coins[:5]:  # Log first 5 for debugging
                    self.logger.debug(f"📊 {coin['symbol']}: funding={coin.get('adjusted_funding', 'N/A'):.6f}, "
                                    f"vol={coin.get('weighted_volatility', 'N/A'):.4f}")
            
            return enriched_coins
            
        except Exception as e:
            self.logger.error(f"❌ Failed to calculate features: {e}")
            raise
    
    async def select_positions(self, enriched_symbols: List[Dict[str, Any]]) -> Tuple[List[Dict], List[Dict]]:
        """
        Select long and short position candidates using top 5 approach

        Uses the shared data analyzer's position selection logic
        with simple ranking by adjusted annualized funding rate.

        Args:
            enriched_symbols: Symbols with calculated features
            
        Returns:
            Tuple of (long_candidates, short_candidates)
        """
        try:
            self.logger.info(f"🎯 Selecting positions from {len(enriched_symbols)} enriched symbols...")
            
            # Get current positions for cost adjustment logic
            try:
                current_positions_list = await self.exchange.fetch_positions()
                current_positions = {}
                for pos in current_positions_list:
                    if pos.get('contracts', 0) != 0:  # Only non-zero positions
                        symbol = pos['symbol']
                        current_positions[symbol] = pos
                self.logger.info(f"📊 Found {len(current_positions)} currently held positions")
            except Exception as e:
                self.logger.warning(f"⚠️ Failed to fetch current positions: {e}")
                current_positions = {}
            
            # Use shared analyzer for position selection
            long_candidates, short_candidates = self.data_analyzer.select_positions_ev_based(
                enriched_symbols, current_positions
            )
            
            self.logger.info(f"✅ Selected {len(long_candidates)} long and {len(short_candidates)} short candidates")
            
            # Log selection details if enabled
            if self.config.get('log_position_selection_details', False):
                if long_candidates:
                    self.logger.info(f"📊 Top 3 long candidates:")
                    for i, candidate in enumerate(long_candidates[:3]):
                        self.logger.info(f"   {i+1}. {candidate['symbol']}: "
                                       f"funding={candidate.get('adjusted_funding', 'N/A'):.6f}, "
                                       f"weight={candidate.get('ev_weight', 'N/A'):.4f}")
                
                if short_candidates:
                    self.logger.info(f"📊 Top 3 short candidates:")
                    for i, candidate in enumerate(short_candidates[:3]):
                        self.logger.info(f"   {i+1}. {candidate['symbol']}: "
                                       f"funding={candidate.get('adjusted_funding', 'N/A'):.6f}, "
                                       f"weight={candidate.get('ev_weight', 'N/A'):.4f}")
            
            return long_candidates, short_candidates
            
        except Exception as e:
            self.logger.error(f"❌ Failed to select positions: {e}")
            raise

    async def size_positions(self, long_candidates: List[Dict],
                           short_candidates: List[Dict]) -> List[StrategyPosition]:
        """
        Calculate position sizes using linear decay weighting and volatility targeting

        Sizing methodology:
        1. Apply linear decay weights based on ranking (same as cross-sectional momentum)
        2. Apply volatility targeting to each position
        3. Contract compliance and price validation
        4. Beta projection disabled for this strategy

        Linear Decay Weights:
        - Long weights: [0.15, 0.125, 0.1, 0.075, 0.05] (top to bottom ranked, sums to 0.5)
        - Short weights: [-0.15, -0.125, -0.1, -0.075, -0.05] (top to bottom ranked, sums to -0.5)

        Args:
            long_candidates: Selected long position candidates (sorted by rank)
            short_candidates: Selected short position candidates (sorted by rank)

        Returns:
            List of StrategyPosition objects with calculated sizes
        """
        try:
            self.logger.info(f"💰 Calculating position sizes for {len(long_candidates)} longs and {len(short_candidates)} shorts...")

            if not long_candidates and not short_candidates:
                self.logger.warning("⚠️ No candidates for position sizing")
                return []

            all_candidates = long_candidates + short_candidates
            target_positions = []

            # Step 1: Calculate linear decay weights
            linear_weights = self._calculate_linear_decay_weights(long_candidates, short_candidates)

            # Step 2: Apply volatility targeting and create positions with contract compliance
            target_positions = await self._create_positions_with_validation(
                all_candidates, linear_weights, long_candidates
            )

            # Beta projection is disabled by default for this strategy
            if self.config.get('enable_beta_projection', False):
                target_positions = await self._apply_beta_projection(target_positions)

            total_capital_allocated = sum(pos.size_usd for pos in target_positions)
            long_count = len([pos for pos in target_positions if pos.side == 'long'])
            short_count = len([pos for pos in target_positions if pos.side == 'short'])

            self.logger.info(f"✅ Position sizing complete:")
            self.logger.info(f"   Total positions: {len(target_positions)}")
            self.logger.info(f"   Long: {long_count} positions")
            self.logger.info(f"   Short: {short_count} positions")
            self.logger.info(f"   Total capital allocated: ${total_capital_allocated:,.0f}")
            self.logger.info(f"   Capital utilization: {total_capital_allocated/self.total_capital:.1%}")

            return target_positions

        except Exception as e:
            self.logger.error(f"❌ Failed to calculate position sizes: {e}")
            raise

    def _calculate_linear_decay_weights(self, long_candidates: List[Dict], short_candidates: List[Dict]) -> List[float]:
        """
        Apply linear decay weights based on position ranking (same as cross-sectional momentum)

        Linear Decay Weights:
        - Long weights: [0.15, 0.125, 0.1, 0.075, 0.05] (top to bottom ranked, sums to 0.5)
        - Short weights: [-0.15, -0.125, -0.1, -0.075, -0.05] (top to bottom ranked, sums to -0.5)

        Args:
            long_candidates: Long position candidates (already sorted by rank)
            short_candidates: Short position candidates (already sorted by rank)

        Returns:
            List of weights for all candidates (longs + shorts)
        """
        try:
            # Predefined linear decay weights for 5 positions per leg
            long_weights = [0.15, 0.125, 0.1, 0.075, 0.05]
            short_weights = [-0.15, -0.125, -0.1, -0.075, -0.05]

            linear_weights = []

            # Apply long weights (top ranked gets highest weight)
            for i in range(len(long_candidates)):
                if i < len(long_weights):
                    linear_weights.append(long_weights[i])
                else:
                    # Fallback for extra positions (shouldn't happen with max_positions_per_leg=5)
                    linear_weights.append(0.01)

            # Apply short weights (top ranked gets most negative weight)
            for i in range(len(short_candidates)):
                if i < len(short_weights):
                    linear_weights.append(short_weights[i])
                else:
                    # Fallback for extra positions (shouldn't happen with max_positions_per_leg=5)
                    linear_weights.append(-0.01)

            self.logger.debug(f"📊 Linear decay weights applied: {len(long_candidates)} longs, {len(short_candidates)} shorts")
            self.logger.debug(f"   Long weights: {linear_weights[:len(long_candidates)]}")
            self.logger.debug(f"   Short weights: {linear_weights[len(long_candidates):]}")

            return linear_weights

        except Exception as e:
            self.logger.error(f"❌ Error calculating linear decay weights: {e}")
            # Fallback to equal weights
            num_longs = len(long_candidates)
            num_shorts = len(short_candidates)
            equal_long_weight = 0.5 / num_longs if num_longs > 0 else 0
            equal_short_weight = -0.5 / num_shorts if num_shorts > 0 else 0

            fallback_weights = [equal_long_weight] * num_longs + [equal_short_weight] * num_shorts
            return fallback_weights

    async def _create_positions_with_validation(self, all_candidates: List[Dict], linear_weights: List[float],
                                              long_candidates: List[Dict]) -> List[StrategyPosition]:
        """
        Create positions with volatility targeting, contract compliance, and price validation
        """
        try:
            # Import contract spec manager for validation
            from utils.contract_specs import contract_spec_manager

            target_positions = []
            target_volatility = self.config.get('target_volatility', 0.20)

            for i, candidate in enumerate(all_candidates):
                base_weight = linear_weights[i]
                side = 'long' if candidate in long_candidates else 'short'

                # Apply volatility targeting
                asset_volatility = candidate.get('weighted_volatility', self.config.get('default_volatility', 0.20))
                if asset_volatility <= 0:
                    asset_volatility = self.config.get('default_volatility', 0.20)

                # Calculate leverage adjustment: target_vol / asset_vol
                leverage_adjustment = target_volatility / asset_volatility

                # Apply adjustment to base weight
                vol_adjusted_weight = abs(base_weight) * leverage_adjustment

                # Calculate position size in USD
                position_size_usd = vol_adjusted_weight * self.total_capital

                # Get current price for position sizing
                price = float(candidate.get('price', candidate.get('last_price', 0)))
                if price <= 0:
                    self.logger.warning(f"⚠️ Invalid price for {candidate['symbol']}: {price}")
                    continue

                # Contract compliance: Calculate position size with rounding
                size_native, actual_position_usd = contract_spec_manager.calculate_position_size_with_rounding(
                    candidate['symbol'], position_size_usd, price)

                # Validate position size calculations
                if not DataUnitValidator.validate_position_size(candidate['symbol'], actual_position_usd, size_native, price):
                    self.logger.error(f"❌ Position size validation failed for {candidate['symbol']}")
                    continue

                # Round price for validation
                rounded_price = contract_spec_manager.round_price(candidate['symbol'], price, round_up=False)

                # Validate contract specifications
                is_valid, error_msg = contract_spec_manager.validate_order_specs(
                    candidate['symbol'], size_native, rounded_price)

                if not is_valid:
                    self.logger.error(f"❌ Contract spec validation failed for {candidate['symbol']}: {error_msg}")
                    continue

                target_positions.append(StrategyPosition(
                    symbol=candidate['symbol'],
                    side=side,
                    size_usd=actual_position_usd,
                    size_native=size_native,
                    weight=vol_adjusted_weight,
                    metadata={
                        'adjusted_funding': candidate.get('adjusted_funding', 0.0),
                        'raw_funding': candidate.get('annualized_funding_3d', 0.0),
                        'weighted_volatility': asset_volatility,
                        'linear_weight': base_weight,
                        'vol_adjusted_weight': vol_adjusted_weight,
                        'position_rank': i + 1,
                        'leverage_adjustment': leverage_adjustment,
                        'strategy_source': 'stat_arb_carry_trade'
                    }
                ))

            return target_positions

        except Exception as e:
            self.logger.error(f"❌ Failed to create positions with validation: {e}")
            return []

    async def _apply_beta_projection(self, positions: List[StrategyPosition]) -> List[StrategyPosition]:
        """Apply beta projection for portfolio beta neutrality (optional)"""
        try:
            if not self.config.get('enable_beta_projection', False):
                return positions

            self.logger.info("🎯 Applying beta projection for portfolio neutrality...")

            # Import beta optimizer
            from execution.beta_optimizer import BetaOptimizer

            # Create beta optimizer with strategy config
            beta_config = {
                'enable_beta_projection': True,
                'beta_neutrality_tolerance': self.config.get('beta_neutrality_tolerance', 0.05),
                'beta_optimization_max_weight_change': self.config.get('beta_optimization_max_weight_change', 0.20)
            }

            beta_optimizer = BetaOptimizer(beta_config)

            # Apply beta optimization
            optimized_positions = await beta_optimizer.optimize_for_beta_neutrality(
                positions, self.data_analyzer.beta_calculator
            )

            if optimized_positions:
                self.logger.info("✅ Beta projection applied successfully")
                return optimized_positions
            else:
                self.logger.warning("⚠️ Beta projection failed, using original positions")
                return positions

        except Exception as e:
            self.logger.warning(f"⚠️ Beta projection failed: {e}, using original positions")
            return positions

    def get_strategy_info(self) -> Dict[str, Any]:
        """Get detailed information about this strategy"""
        base_info = super().get_strategy_info()

        strategy_info = {
            **base_info,
            'description': 'Statistical Arbitrage Carry Trade Strategy',
            'strategy_type': 'funding_arbitrage',
            'universe_selection': 'volume_volatility_filtered_90d_listing_age_no_funding_bounds',
            'position_selection': 'top_5_by_adjusted_annualized_funding_rate',
            'position_sizing': 'linear_decay_weighting_with_volatility_targeting_and_contract_compliance',
            'risk_management': 'portfolio_ev_threshold_and_buffer_zones',
            'exchange': self.exchange_name,
            'total_capital': self.total_capital,
            'key_parameters': {
                'max_positions_per_leg': self.config.get('max_positions_per_leg', 5),
                'target_volatility': self.config.get('target_volatility'),
                'max_position_capital_pct': self.config.get('max_position_capital_pct'),
                'trading_cost_adjustment_annualized': self.config.get('trading_cost_adjustment_annualized', 0.1095),
                'min_daily_volume_usd': self.config.get('min_daily_volume_usd'),
                'exclude_new_listings_days': self.config.get('exclude_new_listings_days', 90),
                'min_historical_data_days': self.config.get('min_historical_data_days', 90)
            }
        }

        return strategy_info

    def validate_strategy_health(self) -> Dict[str, Any]:
        """
        Validate strategy health and configuration

        Returns:
            Dictionary containing health check results
        """
        health_check = {
            'overall_health': 'healthy',
            'issues': [],
            'warnings': [],
            'config_validation': True,
            'data_access': True,
            'exchange_access': True
        }

        try:
            # Validate configuration
            self._validate_config()
        except Exception as e:
            health_check['config_validation'] = False
            health_check['issues'].append(f"Config validation failed: {e}")
            health_check['overall_health'] = 'unhealthy'

        # Check data fetcher access
        if not self.data_fetcher:
            health_check['data_access'] = False
            health_check['issues'].append("Data fetcher not available")
            health_check['overall_health'] = 'unhealthy'

        # Check exchange access
        if not self.exchange:
            health_check['exchange_access'] = False
            health_check['issues'].append("Exchange interface not available")
            health_check['overall_health'] = 'unhealthy'

        # Check for potential warnings
        if self.config.get('max_position_capital_pct', 0) > 50:
            health_check['warnings'].append("High max position capital percentage (>50%)")

        if self.config.get('min_positions_per_leg', 0) < 2:
            health_check['warnings'].append("Low minimum positions per leg (<2) may reduce diversification")

        # Set overall health based on issues
        if health_check['issues']:
            health_check['overall_health'] = 'unhealthy'
        elif health_check['warnings']:
            health_check['overall_health'] = 'warning'

        return health_check
