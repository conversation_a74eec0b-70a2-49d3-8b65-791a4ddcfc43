{"test_structure_verification.py::TestStructureVerification::test_mock_exchange_functionality": true, "test_structure_verification.py::TestTestRunnerFunctionality::test_test_runner_can_be_imported": true, "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_bounds_checking": true, "validation/calculations/test_funding_rate_calculations.py::TestFundingRateUnitConsistency::test_input_units": true, "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_data_fetching_pipeline": true, "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_strategy_execution_pipeline": true, "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_portfolio_combination_pipeline": true, "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_performance_tracking_pipeline": true, "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_error_handling_throughout_system": true, "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_concurrent_operations": true, "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_configuration_edge_cases": true, "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_system_state_consistency": true, "end_to_end/full_system/test_system_integration.py::TestFullSystemWorkflow::test_complete_trading_workflow_simulation": true}