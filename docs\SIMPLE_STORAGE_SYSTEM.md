# Simple Unified Storage System

## Overview

The trading system now uses a single, unified SQLite database for all data storage. This replaces the previous fragmented approach with a simple, efficient, and maintainable solution.

## Key Features

✅ **Single Database**: All data in one `trading_system.db` file  
✅ **Simple API**: Easy-to-use database interface  
✅ **Grafana Compatible**: Direct SQLite integration  
✅ **Efficient Caching**: Simple TTL-based cache  
✅ **Integrity Checks**: Built-in database validation  
✅ **Local Storage**: No external dependencies  

## Database Schema

### Core Tables

1. **strategy_metrics** - Strategy performance data
2. **portfolio_metrics** - Portfolio-level metrics  
3. **market_data** - OHLCV, funding rates, tickers
4. **positions** - Current position tracking
5. **system_state** - Application state storage

### Grafana Integration

The database is designed for direct Grafana access:
- **Data Source**: SQLite connector
- **Path**: `data/trading_system.db`
- **Tables**: Direct SQL queries on metrics tables

## Usage

### Database Operations

```python
from storage.database import get_database

# Get database instance
db = get_database()

# Store strategy metrics
metrics = {
    'timestamp': datetime.now(),
    'total_return': 0.05,
    'sharpe_ratio': 1.2,
    'volatility': 0.15
}
db.store_strategy_metrics("momentum", "bybit", metrics)

# Store portfolio metrics
db.store_portfolio_metrics(portfolio_metrics_dict)

# Store market data
db.store_market_data("bybit", "BTCUSDT", "ohlcv", ohlcv_data, "1d")

# Get positions
positions = db.get_positions("momentum", "bybit")

# System state
db.set_state("last_update", datetime.now())
last_update = db.get_state("last_update")
```

### Caching

```python
from data.cache import get_cache, CacheKeys

cache = get_cache()

# Cache market data
key = CacheKeys.market_data("bybit", "BTCUSDT", "ohlcv", "1d")
cache.set(key, ohlcv_data, ttl=300)

# Retrieve cached data
data = cache.get(key)

# Cache statistics
stats = cache.get_stats()
```

## Configuration

### Database Settings

```yaml
# config.yaml
database:
  path: "data/trading_system.db"
  enable_wal_mode: true
  cache_size_mb: 64
  vacuum_frequency_days: 30
  enable_integrity_checks: true
```

### Grafana Settings

```yaml
basic_metrics:
  enable_grafana_export: true
  grafana_export_frequency_hours: 6
  grafana_datasource_config: "grafana/datasource_config.json"
  grafana_dashboard_config: "grafana/basic_dashboard_config.json"
```

## Setup

### 1. Initialize Database

```bash
python scripts/setup_database.py
```

This will:
- Create necessary directories
- Initialize the unified database
- Setup Grafana configuration
- Validate the setup

### 2. Configure Grafana

1. **Add Data Source**:
   - Type: SQLite
   - Path: `data/trading_system.db`
   - Name: `Trading_System_DB`

2. **Import Dashboard**:
   - Use configuration from `grafana/basic_dashboard_config.json`

### 3. Verify Setup

```python
from storage.database import get_database

db = get_database()

# Check integrity
print(f"Database OK: {db.integrity_check()}")

# View statistics
print(f"Stats: {db.get_stats()}")
```

## File Structure

```
data/
└── trading_system.db              # Single unified database

grafana/
├── datasource_config.json         # SQLite data source config
└── basic_dashboard_config.json    # Dashboard configuration

src/storage/
└── database.py                    # Unified database interface

src/data/
└── cache.py                       # Simple caching system

scripts/
└── setup_database.py              # Setup script
```

## Maintenance

### Database Maintenance

```python
# Integrity check
db.integrity_check()

# Vacuum database (reclaim space)
db.vacuum()

# Get statistics
stats = db.get_stats()
```

### Manual Backup

```bash
# Simple file copy
cp data/trading_system.db backups/trading_system_$(date +%Y%m%d).db

# With compression
gzip -c data/trading_system.db > backups/trading_system_$(date +%Y%m%d).db.gz
```

### Cache Management

```python
# Clear expired items
cache.cleanup_expired()

# Clear all cache
cache.clear()

# View cache statistics
stats = cache.get_stats()
```

## Migration from Old System

The new system automatically replaces the old fragmented approach:

### Removed Files
- `src/portfolio/metrics_database.py`
- `config/metrics_config.yaml`
- `scripts/setup_metrics.py`
- Complex caching and migration systems

### Simplified Files
- `src/portfolio/basic_metrics_database.py` → Simple adapter
- `src/data/cache.py` → Simple TTL cache
- `config.yaml` → Unified database configuration

## Performance

### Database Performance
- **WAL Mode**: Better concurrent access
- **64MB Cache**: Faster queries
- **Proper Indexes**: Optimized for Grafana queries
- **Single File**: Reduced I/O overhead

### Cache Performance
- **Simple LRU**: Fast eviction
- **TTL Support**: Automatic expiration
- **Thread Safe**: Concurrent access
- **Memory Efficient**: Configurable limits

## Troubleshooting

### Database Issues

```python
# Check if database exists
from pathlib import Path
print(f"Database exists: {Path('data/trading_system.db').exists()}")

# Test database connection
try:
    db = get_database()
    print(f"Database OK: {db.integrity_check()}")
except Exception as e:
    print(f"Database error: {e}")
```

### Grafana Connection Issues

1. **Check data source path**: Ensure path points to `data/trading_system.db`
2. **Verify permissions**: Grafana needs read access to database file
3. **Test query**: Try simple `SELECT * FROM portfolio_metrics LIMIT 10`

### Cache Issues

```python
# Check cache status
cache = get_cache()
stats = cache.get_stats()
print(f"Cache hit rate: {stats['hit_rate_percent']:.1f}%")

# Clear cache if needed
cache.clear()
```

## Benefits

### Simplicity
- **Single Database**: One file to manage
- **Simple API**: Easy to understand and use
- **No Dependencies**: Pure SQLite, no external services

### Efficiency
- **Fast Queries**: Optimized for common access patterns
- **Small Footprint**: Minimal resource usage
- **Local Storage**: No network overhead

### Maintainability
- **Clear Structure**: Well-organized schema
- **Easy Backup**: Simple file copy
- **Integrity Checks**: Built-in validation

### Grafana Integration
- **Direct Access**: No intermediate layers
- **Real-time Data**: Live database queries
- **Standard SQL**: Use familiar query syntax
