"""
Test to verify the testing structure is properly set up
"""

import pytest
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))


class TestStructureVerification:
    """Verify that the testing structure is properly set up"""
    
    def test_test_directories_exist(self):
        """Test that all test directories exist"""
        test_root = Path(__file__).parent
        
        expected_dirs = [
            'unit', 'integration', 'end_to_end', 'real_api', 
            'validation', 'fixtures', 'helpers'
        ]
        
        for dir_name in expected_dirs:
            dir_path = test_root / dir_name
            assert dir_path.exists(), f"Test directory {dir_name} does not exist"
            assert dir_path.is_dir(), f"{dir_name} is not a directory"
    
    def test_unit_test_subdirectories_exist(self):
        """Test that unit test subdirectories exist"""
        unit_dir = Path(__file__).parent / 'unit'
        
        expected_subdirs = [
            'config', 'core', 'data', 'exchanges', 'execution',
            'portfolio', 'storage', 'strategies', 'utils'
        ]
        
        for subdir_name in expected_subdirs:
            subdir_path = unit_dir / subdir_name
            assert subdir_path.exists(), f"Unit test subdirectory {subdir_name} does not exist"
            assert subdir_path.is_dir(), f"{subdir_name} is not a directory"
    
    def test_helper_files_exist(self):
        """Test that helper files exist"""
        helpers_dir = Path(__file__).parent / 'helpers'
        
        expected_files = [
            '__init__.py', 'mock_exchanges.py', 'data_generators.py',
            'assertion_helpers.py', 'real_api_helper.py'
        ]
        
        for file_name in expected_files:
            file_path = helpers_dir / file_name
            assert file_path.exists(), f"Helper file {file_name} does not exist"
            assert file_path.is_file(), f"{file_name} is not a file"
    
    def test_fixture_files_exist(self):
        """Test that fixture files exist"""
        fixtures_dir = Path(__file__).parent / 'fixtures'
        
        expected_files = [
            '__init__.py', 'market_data_fixtures.py', 'config_fixtures.py',
            'calculation_fixtures.py'
        ]
        
        for file_name in expected_files:
            file_path = fixtures_dir / file_name
            assert file_path.exists(), f"Fixture file {file_name} does not exist"
            assert file_path.is_file(), f"{file_name} is not a file"
    
    def test_test_runner_exists(self):
        """Test that the test runner exists"""
        test_runner = Path(__file__).parent / 'run_all_tests.py'
        assert test_runner.exists(), "Test runner does not exist"
        assert test_runner.is_file(), "Test runner is not a file"
    
    def test_pytest_config_exists(self):
        """Test that pytest configuration exists"""
        pytest_ini = Path(__file__).parent / 'pytest.ini'
        assert pytest_ini.exists(), "pytest.ini does not exist"
        assert pytest_ini.is_file(), "pytest.ini is not a file"
    
    def test_readme_exists(self):
        """Test that README exists"""
        readme = Path(__file__).parent / 'README.md'
        assert readme.exists(), "README.md does not exist"
        assert readme.is_file(), "README.md is not a file"
    
    def test_can_import_helpers(self):
        """Test that helper modules can be imported"""
        try:
            from tests.helpers import MockExchange, generate_ohlcv_data, assert_calculation_accuracy
            assert MockExchange is not None
            assert generate_ohlcv_data is not None
            assert assert_calculation_accuracy is not None
        except ImportError as e:
            pytest.fail(f"Cannot import helpers: {e}")
    
    def test_can_import_fixtures(self):
        """Test that fixture modules can be imported"""
        try:
            from tests.fixtures import SAMPLE_OHLCV_DATA, MINIMAL_CONFIG, FUNDING_RATE_CALCULATIONS
            assert SAMPLE_OHLCV_DATA is not None
            assert MINIMAL_CONFIG is not None
            assert FUNDING_RATE_CALCULATIONS is not None
        except ImportError as e:
            pytest.fail(f"Cannot import fixtures: {e}")
    
    def test_mock_exchange_functionality(self):
        """Test that MockExchange works"""
        from tests.helpers import MockExchange
        
        mock_exchange = MockExchange()
        assert mock_exchange.name == 'mock'
        assert hasattr(mock_exchange, 'initialize')
        assert hasattr(mock_exchange, 'fetch_ticker')
    
    def test_data_generators_functionality(self):
        """Test that data generators work"""
        from tests.helpers import generate_ohlcv_data, generate_ticker_data
        
        # Test OHLCV generation
        ohlcv = generate_ohlcv_data('BTCUSDT', '1h', 10)
        assert len(ohlcv) == 10
        assert all(len(candle) >= 6 for candle in ohlcv)
        
        # Test ticker generation
        ticker = generate_ticker_data('BTCUSDT')
        assert ticker['symbol'] == 'BTCUSDT'
        assert 'last' in ticker
        assert 'bid' in ticker
        assert 'ask' in ticker
    
    def test_assertion_helpers_functionality(self):
        """Test that assertion helpers work"""
        from tests.helpers import assert_calculation_accuracy, assert_units_consistent
        
        # Test calculation accuracy assertion
        assert_calculation_accuracy(1.0, 1.0, tolerance=1e-10)
        
        # Test units consistency assertion
        assert_units_consistent(0.0001, "rate", "test rate")
        assert_units_consistent(50000.0, "usd", "test price")
    
    def test_sample_unit_test_exists(self):
        """Test that at least one unit test file exists"""
        unit_dir = Path(__file__).parent / 'unit'
        
        # Look for any test file in unit directory
        test_files = list(unit_dir.rglob('test_*.py'))
        assert len(test_files) > 0, "No unit test files found"
        
        # Check that at least one test file has content
        for test_file in test_files[:3]:  # Check first 3 files
            content = test_file.read_text()
            assert len(content) > 100, f"Test file {test_file.name} seems too small"
            assert 'def test_' in content, f"Test file {test_file.name} has no test functions"
    
    def test_validation_test_exists(self):
        """Test that validation tests exist"""
        validation_dir = Path(__file__).parent / 'validation'
        
        # Look for validation test files
        test_files = list(validation_dir.rglob('test_*.py'))
        assert len(test_files) > 0, "No validation test files found"
    
    def test_real_api_test_exists(self):
        """Test that real API tests exist"""
        real_api_dir = Path(__file__).parent / 'real_api'
        
        # Look for real API test files
        test_files = list(real_api_dir.rglob('test_*.py'))
        assert len(test_files) > 0, "No real API test files found"
    
    def test_end_to_end_test_exists(self):
        """Test that end-to-end tests exist"""
        e2e_dir = Path(__file__).parent / 'end_to_end'
        
        # Look for end-to-end test files
        test_files = list(e2e_dir.rglob('test_*.py'))
        assert len(test_files) > 0, "No end-to-end test files found"


class TestTestRunnerFunctionality:
    """Test that the test runner works"""
    
    def test_test_runner_can_be_imported(self):
        """Test that test runner can be imported"""
        test_runner_path = Path(__file__).parent / 'run_all_tests.py'

        # Read the file to check it's valid Python
        content = test_runner_path.read_text(encoding='utf-8')
        assert 'def main(' in content, "Test runner should have main function"
        assert 'TestRunner' in content, "Test runner should have TestRunner class"
    
    def test_pytest_markers_are_defined(self):
        """Test that pytest markers are properly defined"""
        pytest_ini_path = Path(__file__).parent / 'pytest.ini'
        content = pytest_ini_path.read_text(encoding='utf-8')

        expected_markers = ['unit', 'integration', 'validation', 'real_api', 'end_to_end']
        for marker in expected_markers:
            assert marker in content, f"Marker {marker} not defined in pytest.ini"


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
