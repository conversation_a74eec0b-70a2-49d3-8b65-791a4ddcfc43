"""
Integration tests for strategy execution workflow
"""

import pytest
import asyncio
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from tests.helpers.mock_exchanges import MockExchange
from tests.fixtures.config_fixtures import MINIMAL_CONFIG


@pytest.mark.asyncio
@pytest.mark.integration
class TestStrategyExecutionIntegration:
    """Test strategy execution integration"""
    
    @pytest.fixture
    async def mock_exchange(self):
        """Create and initialize mock exchange"""
        exchange = MockExchange()
        await exchange.initialize(MINIMAL_CONFIG)
        return exchange
    
    async def test_basic_strategy_workflow(self, mock_exchange):
        """Test basic strategy workflow"""
        # Simple test that verifies the exchange works
        ticker = await mock_exchange.fetch_ticker("BTCUSDT")
        assert ticker is not None
        
        # Test that we can fetch multiple data points needed for strategies
        ohlcv = await mock_exchange.fetch_ohlcv("BTCUSDT", "1h", 24)
        funding = await mock_exchange.fetch_funding_rate("BTCUSDT")
        
        assert ohlcv is not None
        assert funding is not None
        assert len(ohlcv) == 24
    
    async def test_strategy_data_requirements(self, mock_exchange):
        """Test that strategy data requirements can be met"""
        symbols = ["BTCUSDT", "ETHUSDT"]
        
        for symbol in symbols:
            # Test all data types needed for strategies
            ticker = await mock_exchange.fetch_ticker(symbol)
            ohlcv = await mock_exchange.fetch_ohlcv(symbol, "1h", 50)
            funding = await mock_exchange.fetch_funding_rate(symbol)
            
            assert ticker is not None
            assert ohlcv is not None
            assert funding is not None
            assert len(ohlcv) > 0


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
