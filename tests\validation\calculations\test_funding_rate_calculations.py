"""
Validation tests for funding rate calculations
"""

import pytest
import math
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from tests.fixtures.calculation_fixtures import FUNDING_RATE_CALCULATIONS
from tests.helpers.assertion_helpers import (
    assert_calculation_accuracy, assert_funding_rate_calculation, assert_units_consistent
)


class TestFundingRateCalculations:
    """Test funding rate calculation accuracy and unit consistency"""
    
    def test_bybit_8h_positive_funding_rate(self):
        """Test Bybit 8-hour positive funding rate calculation"""
        test_case = FUNDING_RATE_CALCULATIONS["bybit_8h_positive"]
        
        input_data = test_case["input"]
        expected = test_case["expected"]
        
        # Test annualization calculation
        funding_rate_8h = input_data["funding_rate_8h"]
        calculated_annualized = funding_rate_8h * 3 * 365  # 3 times per day, 365 days
        
        assert_calculation_accuracy(
            calculated_annualized,
            expected["annualized_rate"],
            tolerance=1e-10,
            msg="Bybit 8h positive funding rate annualization"
        )
        
        # Test trading cost adjustment
        trading_cost = input_data["trading_cost"]
        calculated_adjusted = calculated_annualized - trading_cost
        
        assert_calculation_accuracy(
            calculated_adjusted,
            expected["adjusted_rate"],
            tolerance=1e-10,
            msg="Bybit 8h positive funding rate adjustment"
        )
        
        # Test unit consistency
        assert_units_consistent(funding_rate_8h, "rate", "8h funding rate")
        assert_units_consistent(calculated_annualized, "rate", "annualized funding rate")
        assert_units_consistent(calculated_adjusted, "rate", "adjusted funding rate")
    
    def test_bybit_8h_negative_funding_rate(self):
        """Test Bybit 8-hour negative funding rate calculation"""
        test_case = FUNDING_RATE_CALCULATIONS["bybit_8h_negative"]
        
        input_data = test_case["input"]
        expected = test_case["expected"]
        
        # Test annualization calculation
        funding_rate_8h = input_data["funding_rate_8h"]
        calculated_annualized = funding_rate_8h * 3 * 365
        
        assert_calculation_accuracy(
            calculated_annualized,
            expected["annualized_rate"],
            tolerance=1e-10,
            msg="Bybit 8h negative funding rate annualization"
        )
        
        # Test trading cost adjustment (should make it more negative)
        trading_cost = input_data["trading_cost"]
        calculated_adjusted = calculated_annualized - trading_cost
        
        assert_calculation_accuracy(
            calculated_adjusted,
            expected["adjusted_rate"],
            tolerance=1e-10,
            msg="Bybit 8h negative funding rate adjustment"
        )
        
        # Verify that adjustment makes negative rate more negative
        assert calculated_adjusted < calculated_annualized
    
    def test_hyperliquid_1h_funding_rate(self):
        """Test Hyperliquid 1-hour funding rate calculation"""
        test_case = FUNDING_RATE_CALCULATIONS["hyperliquid_1h"]
        
        input_data = test_case["input"]
        expected = test_case["expected"]
        
        # Test annualization calculation (24 times per day for 1h intervals)
        funding_rate_1h = input_data["funding_rate_1h"]
        calculated_annualized = funding_rate_1h * 24 * 365
        
        assert_calculation_accuracy(
            calculated_annualized,
            expected["annualized_rate"],
            tolerance=1e-10,
            msg="Hyperliquid 1h funding rate annualization"
        )
        
        # Test trading cost adjustment
        trading_cost = input_data["trading_cost"]
        calculated_adjusted = calculated_annualized - trading_cost
        
        assert_calculation_accuracy(
            calculated_adjusted,
            expected["adjusted_rate"],
            tolerance=1e-10,
            msg="Hyperliquid 1h funding rate adjustment"
        )
    
    def test_funding_rate_frequency_consistency(self):
        """Test funding rate frequency calculations are consistent"""
        base_rate = 0.0001
        
        # Different exchange frequencies
        frequencies = {
            'bybit': 3,      # 8h intervals = 3 times per day
            'binance': 3,    # 8h intervals = 3 times per day
            'okx': 3,        # 8h intervals = 3 times per day
            'hyperliquid': 24  # 1h intervals = 24 times per day
        }
        
        for exchange, frequency in frequencies.items():
            annualized_rate = base_rate * frequency * 365
            
            # Verify reasonable range
            assert -1.0 <= annualized_rate <= 1.0, f"Unreasonable annualized rate for {exchange}"
            
            # Verify unit consistency
            assert_units_consistent(annualized_rate, "rate", f"{exchange} annualized rate")
    
    def test_trading_cost_adjustment_logic(self):
        """Test trading cost adjustment logic"""
        test_rates = [0.001, -0.001, 0.0, 0.0005, -0.0005]
        trading_cost = 0.1095
        
        for rate in test_rates:
            adjusted_rate = rate - trading_cost
            
            # Adjustment should always reduce the rate (make it less attractive)
            assert adjusted_rate < rate
            
            # Difference should equal trading cost
            assert_calculation_accuracy(
                rate - adjusted_rate,
                trading_cost,
                tolerance=1e-10,
                msg=f"Trading cost adjustment for rate {rate}"
            )
    
    def test_funding_rate_edge_cases(self):
        """Test funding rate calculations with edge cases"""
        # Zero funding rate
        zero_rate = 0.0
        annualized_zero = zero_rate * 3 * 365
        assert annualized_zero == 0.0
        
        # Very small positive rate
        tiny_rate = 1e-8
        annualized_tiny = tiny_rate * 3 * 365
        assert annualized_tiny > 0
        assert annualized_tiny < 0.01  # Should be very small
        
        # Very small negative rate
        tiny_negative = -1e-8
        annualized_tiny_negative = tiny_negative * 3 * 365
        assert annualized_tiny_negative < 0
        assert annualized_tiny_negative > -0.01
    
    def test_funding_rate_mathematical_properties(self):
        """Test mathematical properties of funding rate calculations"""
        base_rate = 0.0001
        
        # Linearity: annualization should be linear
        rate1 = base_rate
        rate2 = base_rate * 2
        
        annualized1 = rate1 * 3 * 365
        annualized2 = rate2 * 3 * 365
        
        assert_calculation_accuracy(
            annualized2,
            annualized1 * 2,
            tolerance=1e-10,
            msg="Funding rate annualization linearity"
        )
        
        # Additivity: adjustment should be additive
        trading_cost = 0.1095
        adjusted1 = annualized1 - trading_cost
        adjusted2 = annualized2 - trading_cost
        
        assert_calculation_accuracy(
            adjusted2 - adjusted1,
            annualized2 - annualized1,
            tolerance=1e-10,
            msg="Trading cost adjustment additivity"
        )
    
    def test_funding_rate_precision(self):
        """Test funding rate calculation precision"""
        # Test with high precision inputs
        precise_rate = 0.000123456789
        
        # Calculate with different methods
        method1 = precise_rate * 3 * 365
        method2 = precise_rate * 1095  # 3 * 365 = 1095
        
        assert_calculation_accuracy(
            method1,
            method2,
            tolerance=1e-15,  # Very high precision
            msg="Funding rate calculation precision"
        )
    
    def test_funding_rate_bounds_checking(self):
        """Test funding rate bounds and reasonableness"""
        # Typical funding rates should be small
        typical_rates = [0.0001, -0.0001, 0.0005, -0.0005, 0.001, -0.001]
        
        for rate in typical_rates:
            annualized = rate * 3 * 365
            
            # Annualized rates should be reasonable (less than 100%)
            assert abs(annualized) < 1.0, f"Unreasonable annualized rate: {annualized}"
            
            # Unit consistency
            assert_units_consistent(rate, "rate", "funding rate")
            assert_units_consistent(annualized, "rate", "annualized funding rate")
    
    def test_funding_rate_sign_preservation(self):
        """Test that funding rate calculations preserve signs correctly"""
        positive_rate = 0.0001
        negative_rate = -0.0001
        
        # Annualization should preserve sign
        pos_annualized = positive_rate * 3 * 365
        neg_annualized = negative_rate * 3 * 365
        
        assert pos_annualized > 0
        assert neg_annualized < 0
        
        # Trading cost adjustment
        trading_cost = 0.1095
        pos_adjusted = pos_annualized - trading_cost
        neg_adjusted = neg_annualized - trading_cost
        
        # Both should become more negative (less attractive)
        assert pos_adjusted < pos_annualized
        assert neg_adjusted < neg_annualized
        
        # Negative rate should become more negative
        assert neg_adjusted < neg_annualized
    
    def test_funding_rate_calculation_consistency(self):
        """Test consistency across different calculation methods"""
        rate = 0.0001
        
        # Method 1: Direct calculation
        annualized1 = rate * 3 * 365
        
        # Method 2: Step by step
        daily_rate = rate * 3
        annualized2 = daily_rate * 365
        
        # Method 3: Using intermediate variables
        frequency = 3
        days_per_year = 365
        annualized3 = rate * frequency * days_per_year
        
        # All methods should give same result
        assert_calculation_accuracy(annualized1, annualized2, tolerance=1e-15)
        assert_calculation_accuracy(annualized1, annualized3, tolerance=1e-15)
        assert_calculation_accuracy(annualized2, annualized3, tolerance=1e-15)


class TestFundingRateUnitConsistency:
    """Test unit consistency in funding rate calculations"""
    
    def test_input_units(self):
        """Test that input units are consistent"""
        # 8-hour funding rates should be small decimals
        rate_8h = 0.0001
        assert_units_consistent(rate_8h, "rate", "8-hour funding rate")
        
        # 1-hour funding rates should be even smaller
        rate_1h = 0.00001
        assert_units_consistent(rate_1h, "rate", "1-hour funding rate")
        
        # Trading costs should be reasonable percentages
        trading_cost = 0.1095  # 10.95%
        assert_units_consistent(trading_cost, "rate", "trading cost")
    
    def test_output_units(self):
        """Test that output units are consistent"""
        rate_8h = 0.0001
        
        # Annualized rate
        annualized = rate_8h * 3 * 365
        assert_units_consistent(annualized, "rate", "annualized funding rate")
        
        # Adjusted rate
        adjusted = annualized - 0.1095
        assert_units_consistent(adjusted, "rate", "adjusted funding rate")
    
    def test_unit_conversion_factors(self):
        """Test unit conversion factors are correct"""
        # 8-hour intervals per day
        intervals_per_day_8h = 24 / 8
        assert intervals_per_day_8h == 3
        
        # 1-hour intervals per day
        intervals_per_day_1h = 24 / 1
        assert intervals_per_day_1h == 24
        
        # Days per year
        days_per_year = 365
        
        # Total intervals per year
        intervals_per_year_8h = intervals_per_day_8h * days_per_year
        assert intervals_per_year_8h == 1095
        
        intervals_per_year_1h = intervals_per_day_1h * days_per_year
        assert intervals_per_year_1h == 8760


if __name__ == '__main__':
    pytest.main([__file__])
