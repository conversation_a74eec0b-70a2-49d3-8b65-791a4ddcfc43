"""
Unit tests for exchange base interface
"""

import pytest
import asyncio
from abc import ABC
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from exchanges.base import ExchangeInterface
from tests.helpers.mock_exchanges import MockExchange


class TestExchangeInterface:
    """Test ExchangeInterface abstract base class"""
    
    def test_exchange_interface_is_abstract(self):
        """Test that ExchangeInterface cannot be instantiated directly"""
        with pytest.raises(TypeError):
            ExchangeInterface()
    
    def test_exchange_interface_inheritance(self):
        """Test that ExchangeInterface inherits from ABC"""
        assert issubclass(ExchangeInterface, ABC)
    
    def test_required_methods_are_abstract(self):
        """Test that required methods are marked as abstract"""
        # Get all abstract methods
        abstract_methods = ExchangeInterface.__abstractmethods__
        
        expected_abstract_methods = {
            'initialize', 'fetch_markets', 'fetch_tickers', 'fetch_ticker',
            'fetch_ohlcv', 'fetch_funding_rate', 'fetch_positions',
            'fetch_balance', 'fetch_order_book', 'create_limit_order',
            'create_market_order', 'cancel_order', 'name'
        }
        
        # Check that all expected methods are abstract
        for method in expected_abstract_methods:
            assert method in abstract_methods, f"{method} should be abstract"
    
    def test_mock_exchange_implements_interface(self):
        """Test that MockExchange properly implements the interface"""
        mock_exchange = MockExchange()
        
        assert isinstance(mock_exchange, ExchangeInterface)
    
    def test_interface_method_signatures(self):
        """Test that interface methods have correct signatures"""
        import inspect
        
        # Test initialize method
        init_sig = inspect.signature(ExchangeInterface.initialize)
        assert 'config' in init_sig.parameters
        
        # Test fetch_ticker method
        ticker_sig = inspect.signature(ExchangeInterface.fetch_ticker)
        assert 'symbol' in ticker_sig.parameters
        
        # Test fetch_ohlcv method
        ohlcv_sig = inspect.signature(ExchangeInterface.fetch_ohlcv)
        params = ohlcv_sig.parameters
        assert 'symbol' in params
        assert 'timeframe' in params
        assert 'limit' in params
        
        # Test create_limit_order method
        limit_order_sig = inspect.signature(ExchangeInterface.create_limit_order)
        params = limit_order_sig.parameters
        assert 'symbol' in params
        assert 'side' in params
        assert 'amount' in params
        assert 'price' in params
        assert 'params' in params


class TestMockExchangeImplementation:
    """Test MockExchange implementation of ExchangeInterface"""
    
    @pytest.fixture
    def mock_exchange(self):
        """Create MockExchange instance"""
        return MockExchange()
    
    @pytest.mark.asyncio
    async def test_initialize_method(self, mock_exchange):
        """Test initialize method implementation"""
        config = {'test': 'config'}
        
        result = await mock_exchange.initialize(config)
        
        assert isinstance(result, bool)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_fetch_markets_method(self, mock_exchange):
        """Test fetch_markets method implementation"""
        await mock_exchange.initialize({})
        
        markets = await mock_exchange.fetch_markets()
        
        assert isinstance(markets, dict)
        assert len(markets) > 0
        
        # Check market structure
        for symbol, market in markets.items():
            assert isinstance(symbol, str)
            assert isinstance(market, dict)
            assert 'symbol' in market
            assert 'base' in market
            assert 'quote' in market
    
    @pytest.mark.asyncio
    async def test_fetch_tickers_method(self, mock_exchange):
        """Test fetch_tickers method implementation"""
        await mock_exchange.initialize({})
        
        tickers = await mock_exchange.fetch_tickers()
        
        assert isinstance(tickers, dict)
        assert len(tickers) > 0
        
        # Check ticker structure
        for symbol, ticker in tickers.items():
            assert isinstance(symbol, str)
            assert isinstance(ticker, dict)
            assert 'symbol' in ticker
            assert 'last' in ticker
            assert 'bid' in ticker
            assert 'ask' in ticker
    
    @pytest.mark.asyncio
    async def test_fetch_ticker_method(self, mock_exchange):
        """Test fetch_ticker method implementation"""
        await mock_exchange.initialize({})
        
        ticker = await mock_exchange.fetch_ticker('BTCUSDT')
        
        assert isinstance(ticker, dict)
        assert ticker['symbol'] == 'BTCUSDT'
        assert 'last' in ticker
        assert 'bid' in ticker
        assert 'ask' in ticker
        assert ticker['last'] > 0
    
    @pytest.mark.asyncio
    async def test_fetch_ohlcv_method(self, mock_exchange):
        """Test fetch_ohlcv method implementation"""
        await mock_exchange.initialize({})
        
        ohlcv = await mock_exchange.fetch_ohlcv('BTCUSDT', '1h', 10)
        
        assert isinstance(ohlcv, list)
        assert len(ohlcv) == 10
        
        # Check OHLCV structure
        for candle in ohlcv:
            assert isinstance(candle, list)
            assert len(candle) >= 6  # timestamp, open, high, low, close, volume
            
            timestamp, open_price, high, low, close, volume = candle[:6]
            assert isinstance(timestamp, (int, float))
            assert isinstance(open_price, (int, float))
            assert isinstance(high, (int, float))
            assert isinstance(low, (int, float))
            assert isinstance(close, (int, float))
            assert isinstance(volume, (int, float))
            
            # Validate price relationships
            assert high >= max(open_price, close)
            assert low <= min(open_price, close)
            assert all(price > 0 for price in [open_price, high, low, close])
            assert volume >= 0
    
    @pytest.mark.asyncio
    async def test_fetch_funding_rate_method(self, mock_exchange):
        """Test fetch_funding_rate method implementation"""
        await mock_exchange.initialize({})
        
        funding = await mock_exchange.fetch_funding_rate('BTCUSDT')
        
        assert isinstance(funding, dict)
        assert funding['symbol'] == 'BTCUSDT'
        assert 'fundingRate' in funding
        assert isinstance(funding['fundingRate'], (int, float))
    
    @pytest.mark.asyncio
    async def test_fetch_positions_method(self, mock_exchange):
        """Test fetch_positions method implementation"""
        await mock_exchange.initialize({})
        
        positions = await mock_exchange.fetch_positions()
        
        assert isinstance(positions, list)
        # Positions can be empty for mock exchange
    
    @pytest.mark.asyncio
    async def test_fetch_balance_method(self, mock_exchange):
        """Test fetch_balance method implementation"""
        await mock_exchange.initialize({})
        
        balance = await mock_exchange.fetch_balance()
        
        assert isinstance(balance, dict)
        assert 'USDT' in balance
        assert isinstance(balance['USDT'], dict)
        assert 'free' in balance['USDT']
        assert 'total' in balance['USDT']
    
    @pytest.mark.asyncio
    async def test_fetch_order_book_method(self, mock_exchange):
        """Test fetch_order_book method implementation"""
        await mock_exchange.initialize({})
        
        orderbook = await mock_exchange.fetch_order_book('BTCUSDT')
        
        assert isinstance(orderbook, dict)
        assert orderbook['symbol'] == 'BTCUSDT'
        assert 'bids' in orderbook
        assert 'asks' in orderbook
        assert isinstance(orderbook['bids'], list)
        assert isinstance(orderbook['asks'], list)
        
        # Check bid/ask structure
        if orderbook['bids']:
            for bid in orderbook['bids']:
                assert len(bid) >= 2  # price, size
                assert bid[0] > 0  # price
                assert bid[1] > 0  # size
        
        if orderbook['asks']:
            for ask in orderbook['asks']:
                assert len(ask) >= 2  # price, size
                assert ask[0] > 0  # price
                assert ask[1] > 0  # size
    
    @pytest.mark.asyncio
    async def test_create_limit_order_method(self, mock_exchange):
        """Test create_limit_order method implementation"""
        await mock_exchange.initialize({})
        
        order = await mock_exchange.create_limit_order(
            'BTCUSDT', 'buy', 0.1, 45000.0, {}
        )
        
        assert isinstance(order, dict)
        assert order['symbol'] == 'BTCUSDT'
        assert order['side'] == 'buy'
        assert order['amount'] == 0.1
        assert order['price'] == 45000.0
        assert order['type'] == 'limit'
        assert 'id' in order
        assert 'status' in order
    
    @pytest.mark.asyncio
    async def test_create_market_order_method(self, mock_exchange):
        """Test create_market_order method implementation"""
        await mock_exchange.initialize({})
        
        order = await mock_exchange.create_market_order(
            'BTCUSDT', 'sell', 0.1, {}
        )
        
        assert isinstance(order, dict)
        assert order['symbol'] == 'BTCUSDT'
        assert order['side'] == 'sell'
        assert order['amount'] == 0.1
        assert order['type'] == 'market'
        assert 'id' in order
        assert 'status' in order
        assert 'price' in order
    
    @pytest.mark.asyncio
    async def test_cancel_order_method(self, mock_exchange):
        """Test cancel_order method implementation"""
        await mock_exchange.initialize({})
        
        # First create an order
        order = await mock_exchange.create_limit_order(
            'BTCUSDT', 'buy', 0.1, 45000.0, {}
        )
        
        # Then cancel it
        cancelled_order = await mock_exchange.cancel_order(order['id'], 'BTCUSDT')
        
        assert isinstance(cancelled_order, dict)
        assert cancelled_order['id'] == order['id']
        assert cancelled_order['status'] == 'canceled'
    
    def test_name_property(self, mock_exchange):
        """Test name property implementation"""
        assert hasattr(mock_exchange, 'name')
        assert isinstance(mock_exchange.name, str)
        assert mock_exchange.name == 'mock'
    
    @pytest.mark.asyncio
    async def test_method_return_types(self, mock_exchange):
        """Test that all methods return expected types"""
        await mock_exchange.initialize({})
        
        # Test async methods return coroutines when called
        methods_to_test = [
            ('fetch_markets', []),
            ('fetch_tickers', []),
            ('fetch_ticker', ['BTCUSDT']),
            ('fetch_ohlcv', ['BTCUSDT', '1h', 10]),
            ('fetch_funding_rate', ['BTCUSDT']),
            ('fetch_positions', []),
            ('fetch_balance', []),
            ('fetch_order_book', ['BTCUSDT']),
            ('create_limit_order', ['BTCUSDT', 'buy', 0.1, 45000.0, {}]),
            ('create_market_order', ['BTCUSDT', 'sell', 0.1, {}]),
        ]
        
        for method_name, args in methods_to_test:
            method = getattr(mock_exchange, method_name)
            result = method(*args)
            
            # Should return a coroutine
            assert asyncio.iscoroutine(result)
            
            # Await the result
            await result
    
    @pytest.mark.asyncio
    async def test_error_handling_interface(self, mock_exchange):
        """Test error handling in interface implementation"""
        await mock_exchange.initialize({})
        
        # Test cancel_order with invalid ID
        with pytest.raises(Exception):
            await mock_exchange.cancel_order('invalid_id', 'BTCUSDT')


if __name__ == '__main__':
    pytest.main([__file__])
