"""
Integration tests for order execution
"""

import pytest
import pytest_asyncio
import asyncio
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from tests.helpers.mock_exchanges import MockExchange
from tests.fixtures.config_fixtures import MINIMAL_CONFIG


@pytest.mark.asyncio
@pytest.mark.integration
class TestOrderExecutionIntegration:
    """Test order execution integration"""

    @pytest_asyncio.fixture
    async def mock_exchange(self):
        """Create and initialize mock exchange"""
        exchange = MockExchange()
        await exchange.initialize(MINIMAL_CONFIG)
        return exchange
    
    async def test_basic_order_execution(self, mock_exchange):
        """Test basic order execution"""
        # Test limit order creation
        order = await mock_exchange.create_limit_order(
            "BTCUSDT", "buy", 0.1, 45000.0, {}
        )
        
        assert order is not None
        assert order['symbol'] == "BTCUSDT"
        assert order['side'] == "buy"
        assert order['type'] == "limit"
        assert 'id' in order
    
    async def test_market_order_execution(self, mock_exchange):
        """Test market order execution"""
        # Test market order creation
        order = await mock_exchange.create_market_order(
            "BTCUSDT", "sell", 0.1, {}
        )
        
        assert order is not None
        assert order['symbol'] == "BTCUSDT"
        assert order['side'] == "sell"
        assert order['type'] == "market"
        assert order['status'] == "closed"
    
    async def test_order_cancellation(self, mock_exchange):
        """Test order cancellation"""
        # Create order first
        order = await mock_exchange.create_limit_order(
            "BTCUSDT", "buy", 0.1, 45000.0, {}
        )
        
        # Then cancel it
        cancelled = await mock_exchange.cancel_order(order['id'], "BTCUSDT")
        
        assert cancelled is not None
        assert cancelled['id'] == order['id']
        assert cancelled['status'] == "canceled"


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
