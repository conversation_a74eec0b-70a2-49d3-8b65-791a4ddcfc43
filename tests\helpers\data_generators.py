"""
Test data generators for creating realistic market data
"""

import random
import time
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional


def generate_ohlcv_data(
    symbol: str,
    timeframe: str = "1h",
    periods: int = 100,
    base_price: float = 45000.0,
    volatility: float = 0.02
) -> List[List]:
    """
    Generate realistic OHLCV data for testing
    
    Args:
        symbol: Trading symbol (e.g., "BTCUSDT")
        timeframe: Timeframe (e.g., "1h", "1d")
        periods: Number of periods to generate
        base_price: Starting price
        volatility: Price volatility (standard deviation)
    
    Returns:
        List of OHLCV data [timestamp, open, high, low, close, volume]
    """
    timeframe_minutes = {
        "1m": 1, "5m": 5, "15m": 15, "30m": 30,
        "1h": 60, "4h": 240, "1d": 1440
    }
    
    interval_minutes = timeframe_minutes.get(timeframe, 60)
    ohlcv_data = []
    
    current_price = base_price
    now = datetime.utcnow()
    
    for i in range(periods):
        # Calculate timestamp
        timestamp = now - timedelta(minutes=interval_minutes * (periods - i - 1))
        ts = int(timestamp.timestamp() * 1000)
        
        # Generate price movement using random walk
        price_change = np.random.normal(0, volatility)
        open_price = current_price
        
        # Generate intraperiod high/low
        high_change = abs(np.random.normal(0, volatility * 0.5))
        low_change = abs(np.random.normal(0, volatility * 0.5))
        
        high_price = open_price * (1 + high_change)
        low_price = open_price * (1 - low_change)
        
        # Close price with trend
        close_price = open_price * (1 + price_change)
        
        # Ensure high >= max(open, close) and low <= min(open, close)
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        # Generate volume (higher volume on larger price moves)
        volume_base = random.uniform(100, 1000)
        volume_multiplier = 1 + abs(price_change) * 10
        volume = volume_base * volume_multiplier
        
        ohlcv_data.append([ts, open_price, high_price, low_price, close_price, volume])
        current_price = close_price
    
    return ohlcv_data


def generate_funding_rate_data(
    symbol: str,
    periods: int = 24,
    base_rate: float = 0.0001
) -> List[Dict[str, Any]]:
    """
    Generate realistic funding rate data
    
    Args:
        symbol: Trading symbol
        periods: Number of funding periods (8h intervals)
        base_rate: Base funding rate
    
    Returns:
        List of funding rate dictionaries
    """
    funding_data = []
    now = datetime.utcnow()
    
    for i in range(periods):
        # Funding times are every 8 hours: 00:00, 08:00, 16:00 UTC
        timestamp = now - timedelta(hours=8 * (periods - i - 1))
        
        # Generate realistic funding rate with some persistence
        rate_change = np.random.normal(0, 0.0001)
        funding_rate = base_rate + rate_change
        
        funding_data.append({
            "symbol": symbol,
            "fundingRate": funding_rate,
            "fundingTimestamp": int(timestamp.timestamp() * 1000),
            "fundingDatetime": timestamp.isoformat(),
            "info": {}
        })
        
        base_rate = funding_rate * 0.8 + rate_change * 0.2  # Add persistence
    
    return funding_data


def generate_ticker_data(symbol: str, base_price: float = 45000.0) -> Dict[str, Any]:
    """Generate realistic ticker data"""
    price_variation = random.uniform(-0.01, 0.01)
    current_price = base_price * (1 + price_variation)
    
    return {
        "symbol": symbol,
        "timestamp": int(time.time() * 1000),
        "datetime": datetime.utcnow().isoformat(),
        "high": current_price * 1.02,
        "low": current_price * 0.98,
        "bid": current_price * 0.9999,
        "ask": current_price * 1.0001,
        "last": current_price,
        "close": current_price,
        "baseVolume": random.uniform(1000, 10000),
        "quoteVolume": random.uniform(1000000, 100000000),
        "info": {}
    }


def generate_orderbook_data(
    symbol: str,
    mid_price: float = 45000.0,
    depth: int = 10,
    spread: float = 0.0001
) -> Dict[str, Any]:
    """Generate realistic orderbook data"""
    bids = []
    asks = []
    
    bid_price = mid_price * (1 - spread / 2)
    ask_price = mid_price * (1 + spread / 2)
    
    for i in range(depth):
        # Generate decreasing bid prices and increasing ask prices
        bid_level = bid_price * (1 - i * 0.0001)
        ask_level = ask_price * (1 + i * 0.0001)
        
        # Generate realistic sizes (larger at better prices)
        bid_size = random.uniform(0.1, 5.0) * (depth - i) / depth
        ask_size = random.uniform(0.1, 5.0) * (depth - i) / depth
        
        bids.append([bid_level, bid_size])
        asks.append([ask_level, ask_size])
    
    return {
        "symbol": symbol,
        "bids": bids,
        "asks": asks,
        "timestamp": int(time.time() * 1000),
        "datetime": datetime.utcnow().isoformat(),
        "nonce": None
    }


def generate_position_data(
    symbol: str,
    side: str,
    size: float,
    entry_price: float
) -> Dict[str, Any]:
    """Generate realistic position data"""
    current_price = entry_price * random.uniform(0.98, 1.02)
    unrealized_pnl = (current_price - entry_price) * size if side == "long" else (entry_price - current_price) * size
    
    return {
        "symbol": symbol,
        "side": side,
        "size": size,
        "contracts": size,
        "contractSize": 1.0,
        "entryPrice": entry_price,
        "markPrice": current_price,
        "notional": size * current_price,
        "unrealizedPnl": unrealized_pnl,
        "percentage": (unrealized_pnl / (size * entry_price)) * 100,
        "timestamp": int(time.time() * 1000),
        "datetime": datetime.utcnow().isoformat(),
        "info": {}
    }


def generate_market_data(
    symbols: List[str],
    timeframe: str = "1h",
    periods: int = 100
) -> Dict[str, Any]:
    """
    Generate comprehensive market data for multiple symbols
    
    Args:
        symbols: List of trading symbols
        timeframe: Data timeframe
        periods: Number of periods
    
    Returns:
        Dictionary containing OHLCV, tickers, funding rates, and orderbooks
    """
    base_prices = {
        "BTCUSDT": 45000, "ETHUSDT": 3000, "ADAUSDT": 0.5,
        "SOLUSDT": 100, "DOTUSDT": 8, "LINKUSDT": 15,
        "AVAXUSDT": 25, "MATICUSDT": 0.8, "ATOMUSDT": 12,
        "NEARUSDT": 3, "FTMUSDT": 0.3, "ALGOUSDT": 0.2
    }
    
    market_data = {
        "ohlcv": {},
        "tickers": {},
        "funding_rates": {},
        "orderbooks": {}
    }
    
    for symbol in symbols:
        base_price = base_prices.get(symbol, 100.0)
        
        # Generate OHLCV data
        market_data["ohlcv"][symbol] = generate_ohlcv_data(
            symbol, timeframe, periods, base_price
        )
        
        # Generate ticker data
        market_data["tickers"][symbol] = generate_ticker_data(symbol, base_price)
        
        # Generate funding rate data
        market_data["funding_rates"][symbol] = generate_funding_rate_data(symbol)
        
        # Generate orderbook data
        current_price = market_data["tickers"][symbol]["last"]
        market_data["orderbooks"][symbol] = generate_orderbook_data(symbol, current_price)
    
    return market_data


def generate_strategy_features(
    symbols: List[str],
    strategy_type: str = "stat_arb"
) -> List[Dict[str, Any]]:
    """
    Generate strategy-specific features for testing
    
    Args:
        symbols: List of symbols
        strategy_type: Type of strategy ("stat_arb", "momentum", "trend")
    
    Returns:
        List of feature dictionaries
    """
    features = []
    
    for symbol in symbols:
        if strategy_type == "stat_arb":
            feature = {
                "symbol": symbol,
                "funding_rate": random.uniform(-0.001, 0.001),
                "annualized_funding_rate": random.uniform(-0.365, 0.365),
                "adjusted_funding_rate": random.uniform(-0.365, 0.365),
                "volatility": random.uniform(0.1, 0.5),
                "volume_usd": random.uniform(1000000, 100000000),
                "listing_age_days": random.randint(90, 1000)
            }
        elif strategy_type == "momentum":
            feature = {
                "symbol": symbol,
                "momentum_score": random.uniform(-3, 3),
                "z_score": random.uniform(-2, 2),
                "volatility": random.uniform(0.1, 0.5),
                "volume_usd": random.uniform(1000000, 100000000),
                "market_cap_rank": random.randint(1, 100)
            }
        elif strategy_type == "trend":
            feature = {
                "symbol": symbol,
                "trend_signal": random.uniform(-1, 1),
                "ema_short": random.uniform(40000, 50000),
                "ema_long": random.uniform(39000, 49000),
                "volatility": random.uniform(0.1, 0.5),
                "volume_usd": random.uniform(1000000, 100000000)
            }
        else:
            feature = {"symbol": symbol}
        
        features.append(feature)
    
    return features
