[tool:pytest]
# Pytest configuration for comprehensive testing

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    validation: Validation tests for calculations and units
    real_api: Real API tests using demo/testnet
    end_to_end: End-to-end system tests
    slow: Slow tests that take more than 30 seconds
    network: Tests that require network connectivity
    database: Tests that require database operations

# Output options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Asyncio mode
asyncio_mode = auto

# Coverage options (when using --cov)
[coverage:run]
source = src
omit = 
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */migrations/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

# Show missing lines
show_missing = true

# Fail if coverage is below threshold
fail_under = 80

[coverage:html]
directory = htmlcov
