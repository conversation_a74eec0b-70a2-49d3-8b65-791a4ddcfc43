#!/usr/bin/env python3
"""
Setup script for unified trading database

Initializes the unified database and verifies the setup.
"""

import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from storage.database import get_database
import yaml

logger = logging.getLogger(__name__)


def load_config():
    """Load configuration from config.yaml"""
    try:
        with open("config.yaml", 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"❌ Failed to load config.yaml: {e}")
        return None


def create_directories():
    """Create necessary directories"""
    directories = [
        "data",
        "grafana",
        "logs",
        "backups"
    ]
    
    print("📁 Creating directories...")
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True


def initialize_database():
    """Initialize the unified database"""
    print("🗄️ Initializing unified database...")
    
    try:
        # Load configuration
        config = load_config()
        if not config:
            return False
        
        # Get database path from config
        db_config = config.get('database', {})
        db_path = db_config.get('path', 'data/trading_system.db')
        
        # Initialize database
        db = get_database(db_path)
        
        # Test database operations
        test_data = {
            'timestamp': '2024-01-01T00:00:00Z',
            'total_return': 0.0,
            'sharpe_ratio': 0.0,
            'volatility': 0.0
        }
        
        # Test storing and retrieving data
        success = db.store_portfolio_metrics(test_data)
        if success:
            print(f"✅ Database initialized and tested: {db_path}")
        else:
            print(f"❌ Database test failed")
            return False
        
        # Check integrity
        if db.integrity_check():
            print("✅ Database integrity check passed")
        else:
            print("❌ Database integrity check failed")
            return False
        
        # Show stats
        stats = db.get_stats()
        print(f"📊 Database stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        return False


def setup_grafana_config():
    """Setup Grafana configuration files"""
    print("📊 Setting up Grafana configuration...")
    
    try:
        # Check if dashboard configuration exists
        dashboard_config_path = Path("grafana/basic_dashboard_config.json")
        
        if dashboard_config_path.exists():
            print("✅ Grafana dashboard configuration exists")
        else:
            print("⚠️ Grafana dashboard configuration not found")
            print("   Please ensure grafana/basic_dashboard_config.json exists")
        
        # Check if data source configuration exists
        datasource_path = Path("grafana/datasource_config.json")
        
        if datasource_path.exists():
            print("✅ Grafana data source configuration exists")
        else:
            print("⚠️ Grafana data source configuration not found")
            print("   Please ensure grafana/datasource_config.json exists")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to setup Grafana configuration: {e}")
        return False


def validate_setup():
    """Validate the complete setup"""
    print("🔍 Validating setup...")
    
    validation_checks = []
    
    # Check if required directories exist
    required_dirs = ["data", "grafana", "logs"]
    for directory in required_dirs:
        exists = Path(directory).exists()
        validation_checks.append((f"Directory {directory}", exists))
    
    # Check if configuration files exist
    config_files = [
        "config.yaml",
        "grafana/basic_dashboard_config.json",
        "grafana/datasource_config.json"
    ]
    for config_file in config_files:
        exists = Path(config_file).exists()
        validation_checks.append((f"Config file {config_file}", exists))
    
    # Check database
    try:
        db = get_database()
        db_exists = db.integrity_check()
        validation_checks.append(("Database integrity", db_exists))
    except Exception:
        validation_checks.append(("Database integrity", False))
    
    # Print validation results
    all_passed = True
    for check_name, passed in validation_checks:
        status = "✅" if passed else "❌"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    return all_passed


def main():
    """Main setup function"""
    print("🚀 Setting up unified trading database system")
    print("=" * 50)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    steps = [
        ("Creating directories", create_directories),
        ("Initializing database", initialize_database),
        ("Setting up Grafana config", setup_grafana_config),
        ("Validating setup", validate_setup)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📦 {step_name}...")
        if not step_func():
            print(f"❌ {step_name} failed!")
            return False
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Configure Grafana to use the SQLite data source")
    print("2. Import the dashboard configuration")
    print("3. Start the trading system")
    print("4. Monitor metrics in Grafana")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
