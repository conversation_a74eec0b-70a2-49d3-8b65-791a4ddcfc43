"""
Unit tests for data analyzer module
"""

import pytest
import math
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from data.analyzer import DataAnalyzer
from tests.helpers.mock_exchanges import MockExchange
from tests.fixtures.market_data_fixtures import SAMPLE_OHLCV_DATA, SAMPLE_FUNDING_RATES
from tests.fixtures.calculation_fixtures import (
    FUNDING_RATE_CALCULATIONS, VOLATILITY_CALCULATIONS
)
from tests.helpers.assertion_helpers import assert_calculation_accuracy, assert_funding_rate_calculation


class TestDataAnalyzer:
    """Test DataAnalyzer class"""
    
    @pytest.fixture
    def mock_exchange(self):
        """Create mock exchange for testing"""
        return MockExchange()
    
    @pytest.fixture
    def mock_config(self):
        """Create mock configuration"""
        return {
            'exchange': 'bybit',
            'trading_cost_adjustment': 0.1095,
            'min_volatility_threshold': 0.05,
            'exclude_new_listings_days': 60
        }
    
    @pytest.fixture
    async def data_analyzer(self, mock_exchange, mock_config):
        """Create DataAnalyzer instance for testing"""
        await mock_exchange.initialize({})
        return DataAnalyzer(mock_exchange, mock_config)
    
    @pytest.mark.asyncio
    async def test_calculate_volatility_from_ohlcv(self, data_analyzer):
        """Test volatility calculation from OHLCV data"""
        ohlcv_data = SAMPLE_OHLCV_DATA["BTCUSDT"]
        
        volatility = data_analyzer.calculate_volatility_from_ohlcv(ohlcv_data)
        
        assert volatility is not None
        assert isinstance(volatility, float)
        assert volatility > 0
        assert volatility < 5.0  # Reasonable upper bound
    
    def test_calculate_volatility_empty_data(self, data_analyzer):
        """Test volatility calculation with empty data"""
        volatility = data_analyzer.calculate_volatility_from_ohlcv([])
        
        # Should return default volatility or None
        assert volatility is None or volatility == data_analyzer.config.get('default_volatility', 0.2)
    
    def test_calculate_volatility_insufficient_data(self, data_analyzer):
        """Test volatility calculation with insufficient data"""
        # Only one data point
        single_candle = [SAMPLE_OHLCV_DATA["BTCUSDT"][0]]
        
        volatility = data_analyzer.calculate_volatility_from_ohlcv(single_candle)
        
        # Should handle gracefully
        assert volatility is None or isinstance(volatility, float)
    
    def test_calculate_returns_from_ohlcv(self, data_analyzer):
        """Test returns calculation from OHLCV data"""
        ohlcv_data = SAMPLE_OHLCV_DATA["BTCUSDT"]
        
        returns = data_analyzer.calculate_returns_from_ohlcv(ohlcv_data)
        
        assert returns is not None
        assert isinstance(returns, list)
        assert len(returns) == len(ohlcv_data) - 1  # One less than input data
        
        # Validate returns are reasonable
        for ret in returns:
            assert isinstance(ret, float)
            assert -1.0 < ret < 1.0  # Returns should be reasonable
    
    def test_calculate_log_returns(self, data_analyzer):
        """Test log returns calculation"""
        prices = [100, 110, 121, 133.1, 146.41]
        
        log_returns = data_analyzer.calculate_log_returns(prices)
        
        assert len(log_returns) == len(prices) - 1
        
        # Validate log returns
        for i, log_ret in enumerate(log_returns):
            expected = math.log(prices[i + 1] / prices[i])
            assert_calculation_accuracy(log_ret, expected, tolerance=1e-10)
    
    def test_annualize_funding_rate_bybit(self, data_analyzer):
        """Test funding rate annualization for Bybit (8h frequency)"""
        test_case = FUNDING_RATE_CALCULATIONS["bybit_8h_positive"]
        
        funding_rate_8h = test_case["input"]["funding_rate_8h"]
        expected_annualized = test_case["expected"]["annualized_rate"]
        
        # Set exchange to bybit
        data_analyzer.exchange.name = "bybit"
        
        annualized_rate = data_analyzer.annualize_funding_rate(funding_rate_8h)
        
        assert_funding_rate_calculation(
            funding_rate_8h, annualized_rate, "bybit"
        )
    
    def test_annualize_funding_rate_hyperliquid(self, data_analyzer):
        """Test funding rate annualization for Hyperliquid (1h frequency)"""
        test_case = FUNDING_RATE_CALCULATIONS["hyperliquid_1h"]
        
        funding_rate_1h = test_case["input"]["funding_rate_1h"]
        
        # Set exchange to hyperliquid
        data_analyzer.exchange.name = "hyperliquid"
        
        annualized_rate = data_analyzer.annualize_funding_rate(funding_rate_1h)
        
        assert_funding_rate_calculation(
            funding_rate_1h, annualized_rate, "hyperliquid"
        )
    
    def test_adjust_funding_rate_for_trading_costs(self, data_analyzer):
        """Test funding rate adjustment for trading costs"""
        test_case = FUNDING_RATE_CALCULATIONS["bybit_8h_positive"]
        
        annualized_rate = test_case["expected"]["annualized_rate"]
        trading_cost = test_case["input"]["trading_cost"]
        expected_adjusted = test_case["expected"]["adjusted_rate"]
        
        adjusted_rate = data_analyzer.adjust_funding_rate_for_trading_costs(
            annualized_rate, trading_cost
        )
        
        assert_calculation_accuracy(adjusted_rate, expected_adjusted)
    
    def test_calculate_ema(self, data_analyzer):
        """Test EMA calculation"""
        prices = [100, 102, 101, 103, 105, 104, 106, 108, 107, 109]
        period = 5
        
        ema_values = data_analyzer.calculate_ema(prices, period)
        
        assert len(ema_values) == len(prices)
        assert all(isinstance(val, float) for val in ema_values)
        
        # EMA should be smoother than raw prices
        # Last EMA value should be close to recent prices
        assert 100 < ema_values[-1] < 110
    
    def test_calculate_z_score(self, data_analyzer):
        """Test z-score calculation"""
        values = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        
        z_scores = data_analyzer.calculate_z_score(values, lookback=5)
        
        assert len(z_scores) == len(values)
        
        # First few values should be None (insufficient lookback)
        assert all(z is None for z in z_scores[:4])
        
        # Later values should be valid z-scores
        valid_z_scores = [z for z in z_scores if z is not None]
        assert len(valid_z_scores) > 0
        assert all(isinstance(z, float) for z in valid_z_scores)
    
    def test_calculate_momentum_score(self, data_analyzer):
        """Test momentum score calculation"""
        returns = [0.01, -0.02, 0.015, -0.01, 0.005, 0.02, -0.015, 0.01]
        lookback = 5
        
        momentum_scores = data_analyzer.calculate_momentum_score(returns, lookback)
        
        assert len(momentum_scores) == len(returns)
        
        # First few values should be None
        assert all(m is None for m in momentum_scores[:lookback-1])
        
        # Valid momentum scores should be reasonable
        valid_scores = [m for m in momentum_scores if m is not None]
        assert len(valid_scores) > 0
        assert all(isinstance(m, float) for m in valid_scores)
    
    def test_calculate_trend_signal(self, data_analyzer):
        """Test trend signal calculation"""
        prices = [100, 102, 101, 103, 105, 104, 106, 108, 107, 109]
        short_period = 3
        long_period = 5
        
        trend_signals = data_analyzer.calculate_trend_signal(
            prices, short_period, long_period
        )
        
        assert len(trend_signals) == len(prices)
        
        # First few values should be None (insufficient data for EMA)
        assert all(t is None for t in trend_signals[:long_period-1])
        
        # Valid trend signals should be reasonable
        valid_signals = [t for t in trend_signals if t is not None]
        assert len(valid_signals) > 0
        assert all(isinstance(t, float) for t in valid_signals)
        assert all(-1 <= t <= 1 for t in valid_signals)  # Normalized signals
    
    def test_calculate_weighted_volatility(self, data_analyzer):
        """Test weighted volatility calculation"""
        volatilities = {
            '10d': 0.15,
            '30d': 0.20,
            '60d': 0.25
        }
        
        weights = {
            '10d': 0.2,
            '30d': 0.5,
            '60d': 0.3
        }
        
        weighted_vol = data_analyzer.calculate_weighted_volatility(volatilities, weights)
        
        expected = 0.15 * 0.2 + 0.20 * 0.5 + 0.25 * 0.3
        assert_calculation_accuracy(weighted_vol, expected)
    
    def test_calculate_beta(self, data_analyzer):
        """Test beta calculation"""
        asset_returns = [0.01, -0.02, 0.015, -0.01, 0.005]
        market_returns = [0.008, -0.015, 0.012, -0.008, 0.004]
        
        beta = data_analyzer.calculate_beta(asset_returns, market_returns)
        
        assert isinstance(beta, float)
        assert -5 < beta < 5  # Reasonable beta range
    
    def test_calculate_correlation(self, data_analyzer):
        """Test correlation calculation"""
        returns1 = [0.01, -0.02, 0.015, -0.01, 0.005]
        returns2 = [0.008, -0.015, 0.012, -0.008, 0.004]
        
        correlation = data_analyzer.calculate_correlation(returns1, returns2)
        
        assert isinstance(correlation, float)
        assert -1 <= correlation <= 1  # Correlation bounds
    
    def test_filter_by_volume(self, data_analyzer):
        """Test volume filtering"""
        symbols_data = [
            {'symbol': 'BTCUSDT', 'volume_usd': 50000000},
            {'symbol': 'ETHUSDT', 'volume_usd': 30000000},
            {'symbol': 'SMALLCOIN', 'volume_usd': 500000}
        ]
        
        min_volume = 1000000
        
        filtered = data_analyzer.filter_by_volume(symbols_data, min_volume)
        
        assert len(filtered) == 3  # All should pass
        
        # Test with higher threshold
        min_volume = 10000000
        filtered = data_analyzer.filter_by_volume(symbols_data, min_volume)
        
        assert len(filtered) == 2  # Only BTC and ETH should pass
        assert all(data['volume_usd'] >= min_volume for data in filtered)
    
    def test_filter_by_volatility(self, data_analyzer):
        """Test volatility filtering"""
        symbols_data = [
            {'symbol': 'BTCUSDT', 'volatility': 0.25},
            {'symbol': 'ETHUSDT', 'volatility': 0.30},
            {'symbol': 'STABLECOIN', 'volatility': 0.02}
        ]
        
        min_volatility = 0.05
        
        filtered = data_analyzer.filter_by_volatility(symbols_data, min_volatility)
        
        assert len(filtered) == 2  # BTC and ETH should pass
        assert all(data['volatility'] >= min_volatility for data in filtered)
    
    def test_filter_by_listing_age(self, data_analyzer):
        """Test listing age filtering"""
        symbols_data = [
            {'symbol': 'BTCUSDT', 'listing_age_days': 365},
            {'symbol': 'ETHUSDT', 'listing_age_days': 200},
            {'symbol': 'NEWCOIN', 'listing_age_days': 30}
        ]
        
        min_age_days = 60
        
        filtered = data_analyzer.filter_by_listing_age(symbols_data, min_age_days)
        
        assert len(filtered) == 2  # BTC and ETH should pass
        assert all(data['listing_age_days'] >= min_age_days for data in filtered)
    
    def test_rank_by_score(self, data_analyzer):
        """Test ranking by score"""
        symbols_data = [
            {'symbol': 'A', 'score': 1.5},
            {'symbol': 'B', 'score': -0.5},
            {'symbol': 'C', 'score': 0.8},
            {'symbol': 'D', 'score': -1.2},
            {'symbol': 'E', 'score': 0.3}
        ]
        
        ranked = data_analyzer.rank_by_score(symbols_data, 'score', ascending=False)
        
        # Should be sorted by score descending
        scores = [item['score'] for item in ranked]
        assert scores == sorted(scores, reverse=True)
        
        # Test ascending order
        ranked_asc = data_analyzer.rank_by_score(symbols_data, 'score', ascending=True)
        scores_asc = [item['score'] for item in ranked_asc]
        assert scores_asc == sorted(scores_asc)
    
    def test_select_top_positions(self, data_analyzer):
        """Test top position selection"""
        symbols_data = [
            {'symbol': 'A', 'score': 1.5},
            {'symbol': 'B', 'score': -0.5},
            {'symbol': 'C', 'score': 0.8},
            {'symbol': 'D', 'score': -1.2},
            {'symbol': 'E', 'score': 0.3}
        ]
        
        # Select top 2 for longs, top 2 for shorts
        top_longs, top_shorts = data_analyzer.select_top_positions(
            symbols_data, 'score', num_longs=2, num_shorts=2
        )
        
        assert len(top_longs) == 2
        assert len(top_shorts) == 2
        
        # Top longs should have highest scores
        long_scores = [item['score'] for item in top_longs]
        assert long_scores == sorted(long_scores, reverse=True)
        
        # Top shorts should have lowest scores
        short_scores = [item['score'] for item in top_shorts]
        assert short_scores == sorted(short_scores)


if __name__ == '__main__':
    pytest.main([__file__])
