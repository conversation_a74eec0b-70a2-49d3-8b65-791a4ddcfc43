"""
Unit tests for configuration settings module
"""

import pytest
import os
import tempfile
import yaml
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from config.settings import Config, load_config
from tests.fixtures.config_fixtures import MINIMAL_CONFIG, FULL_CONFIG, INVALID_CONFIGS


class TestConfig:
    """Test Config class functionality"""
    
    def test_config_initialization_with_dict(self):
        """Test Config initialization with dictionary"""
        config = Config(MINIMAL_CONFIG.copy())

        assert config.get('exchange') == 'bybit'
        assert config.get('total_capital_usd') == 1000
        assert config.get('simulation_mode') is True
    
    def test_config_get_with_default(self):
        """Test Config.get() with default values"""
        config = Config(MINIMAL_CONFIG.copy())

        # Existing key
        assert config.get('exchange') == 'bybit'

        # Non-existing key with default
        assert config.get('non_existing_key', 'default_value') == 'default_value'

        # Non-existing key without default
        assert config.get('non_existing_key') is None
    
    def test_config_set(self):
        """Test Config.set() method"""
        config = Config(MINIMAL_CONFIG.copy())

        # Set new value
        config.set('new_key', 'new_value')
        assert config.get('new_key') == 'new_value'

        # Update existing value
        config.set('exchange', 'binance')
        assert config.get('exchange') == 'binance'
    
    def test_config_to_dict(self):
        """Test Config.to_dict() method"""
        # Clear environment variables that might interfere
        old_exchange = os.environ.get('EXCHANGE')
        if 'EXCHANGE' in os.environ:
            del os.environ['EXCHANGE']

        try:
            config = Config(MINIMAL_CONFIG.copy())
            config_dict = config.to_dict()

            assert isinstance(config_dict, dict)
            assert config_dict['exchange'] == 'bybit'
            assert config_dict['total_capital_usd'] == 1000
        finally:
            # Restore environment variable
            if old_exchange is not None:
                os.environ['EXCHANGE'] = old_exchange
    
    def test_config_nested_access(self):
        """Test accessing nested configuration values"""
        config = Config(FULL_CONFIG.copy())

        # Test nested dictionary access
        strategies = config.get('strategies', {})
        assert isinstance(strategies, dict)
        assert 'stat_arb_carry_trade' in strategies

        # Test deep nested access
        stat_arb_config = strategies.get('stat_arb_carry_trade', {})
        assert stat_arb_config.get('enabled') is True
        assert stat_arb_config.get('weight') == 0.33
    
    def test_config_update(self):
        """Test Config update functionality"""
        config = Config(MINIMAL_CONFIG.copy())

        # Update with new dictionary
        update_dict = {
            'exchange': 'binance',
            'new_parameter': 'new_value'
        }

        config._config.update(update_dict)

        assert config.get('exchange') == 'binance'
        assert config.get('new_parameter') == 'new_value'
        assert config.get('total_capital_usd') == 1000  # Original value preserved


class TestLoadConfig:
    """Test load_config function"""
    
    def test_load_config_from_file(self):
        """Test loading configuration from YAML file"""
        # Clear environment variables that might interfere
        old_exchange = os.environ.get('EXCHANGE')
        if 'EXCHANGE' in os.environ:
            del os.environ['EXCHANGE']

        try:
            # Create temporary config file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                yaml.dump(MINIMAL_CONFIG, f)
                temp_file = f.name

            try:
                config = load_config(temp_file)

                assert isinstance(config, Config)
                assert config.get('exchange') == 'bybit'
                assert config.get('total_capital_usd') == 1000
            finally:
                os.unlink(temp_file)
        finally:
            # Restore environment variable
            if old_exchange is not None:
                os.environ['EXCHANGE'] = old_exchange
    
    def test_load_config_nonexistent_file(self):
        """Test loading configuration from non-existent file"""
        # Clear environment variables that might interfere
        old_exchange = os.environ.get('EXCHANGE')
        if 'EXCHANGE' in os.environ:
            del os.environ['EXCHANGE']

        try:
            config = load_config('nonexistent_file.yaml')

            # Should return default configuration
            assert isinstance(config, Config)
            assert config.get('exchange') == 'bybit'  # Default value
            assert config.get('total_capital_usd') == 10000  # Default value
        finally:
            # Restore environment variable
            if old_exchange is not None:
                os.environ['EXCHANGE'] = old_exchange
    
    def test_load_config_none_parameter(self):
        """Test loading configuration with None parameter"""
        # Clear environment variables that might interfere
        old_exchange = os.environ.get('EXCHANGE')
        if 'EXCHANGE' in os.environ:
            del os.environ['EXCHANGE']

        try:
            config = load_config(None)

            # Should return default configuration
            assert isinstance(config, Config)
            assert config.get('exchange') == 'bybit'
        finally:
            # Restore environment variable
            if old_exchange is not None:
                os.environ['EXCHANGE'] = old_exchange
    
    def test_load_config_invalid_yaml(self):
        """Test loading configuration from invalid YAML file"""
        # Create temporary invalid YAML file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            temp_file = f.name
        
        try:
            config = load_config(temp_file)
            
            # Should return default configuration on error
            assert isinstance(config, Config)
            assert config.get('exchange') == 'bybit'
        finally:
            os.unlink(temp_file)
    
    def test_load_config_with_environment_override(self):
        """Test configuration loading with environment variable override"""
        # Clear any existing environment variables that might interfere
        old_exchange = os.environ.get('EXCHANGE')
        if 'EXCHANGE' in os.environ:
            del os.environ['EXCHANGE']

        try:
            # Set environment variable for exchange override
            os.environ['EXCHANGE'] = 'okx'

            # Create temporary config file
            test_config = {
                'exchange': 'binance',  # This should be overridden by env var
                'total_capital_usd': 5000,
                'simulation_mode': False
            }

            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                yaml.dump(test_config, f)
                temp_file = f.name

            try:
                config = load_config(temp_file)

                # Environment variable should override file config
                assert config.get('exchange') == 'okx'
                assert config.get('total_capital_usd') == 5000
                assert config.get('simulation_mode') is False

            finally:
                os.unlink(temp_file)

        finally:
            # Cleanup environment variables
            if 'EXCHANGE' in os.environ:
                del os.environ['EXCHANGE']
            if old_exchange is not None:
                os.environ['EXCHANGE'] = old_exchange
    
    def test_load_config_merge_with_defaults(self):
        """Test that loaded config merges with defaults"""
        # Create minimal config file
        minimal_config = {'exchange': 'binance'}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(minimal_config, f)
            temp_file = f.name
        
        try:
            config = load_config(temp_file)
            
            # Should have loaded value
            assert config.get('exchange') == 'binance'
            
            # Should have default values for missing keys
            assert config.get('total_capital_usd') == 10000
            assert config.get('use_testnet') is True
            assert config.get('simulation_mode') is True
            
        finally:
            os.unlink(temp_file)
    
    def test_load_config_complex_nested_structure(self):
        """Test loading complex nested configuration"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(FULL_CONFIG, f)
            temp_file = f.name
        
        try:
            config = load_config(temp_file)
            
            # Test top-level values
            assert config.get('exchange') == 'bybit'
            assert config.get('total_capital_usd') == 10000
            
            # Test nested structures
            strategies = config.get('strategies', {})
            assert len(strategies) == 3
            assert strategies['stat_arb_carry_trade']['enabled'] is True
            
            portfolio_config = config.get('portfolio_combination', {})
            assert portfolio_config['enable_position_netting'] is True
            assert portfolio_config['portfolio_target_volatility'] == 0.20
            
            api_config = config.get('api_rate_limiting', {})
            assert api_config['max_requests_per_second'] == 5
            
        finally:
            os.unlink(temp_file)


class TestConfigDefaults:
    """Test default configuration values"""
    
    def test_default_config_structure(self):
        """Test that default configuration has required structure"""
        config = load_config(None)  # Load defaults
        
        # Required top-level keys
        required_keys = [
            'exchange', 'total_capital_usd', 'use_testnet', 'use_demo',
            'simulation_mode', 'min_daily_volume_usd'
        ]
        
        for key in required_keys:
            assert config.get(key) is not None, f"Missing required default key: {key}"
    
    def test_default_exchange_settings(self):
        """Test default exchange settings"""
        config = load_config(None)
        
        assert config.get('exchange') == 'bybit'
        assert config.get('use_testnet') is True
        assert config.get('use_demo') is False
    
    def test_default_strategy_settings(self):
        """Test default strategy settings"""
        config = load_config(None)
        
        assert config.get('total_capital_usd') == 10000
        assert config.get('min_daily_volume_usd') == 1000000
        assert config.get('max_abs_funding_rate') == 0.0030
    
    def test_default_execution_settings(self):
        """Test default execution settings"""
        config = load_config(None)
        
        assert config.get('min_batch_size') == 1
        assert config.get('max_batch_size') == 5
        assert config.get('min_batch_interval_seconds') == 30
        assert config.get('max_batch_interval_seconds') == 300
    
    def test_default_risk_settings(self):
        """Test default risk management settings"""
        config = load_config(None)
        
        assert config.get('min_volatility_threshold') == 0.05
        assert config.get('max_position_capital_pct') == 25
        assert config.get('min_positions_per_leg') == 2


class TestConfigTypes:
    """Test configuration value types"""
    
    def test_numeric_types(self):
        """Test that numeric configuration values have correct types"""
        config = Config(FULL_CONFIG)
        
        # Integer values
        assert isinstance(config.get('total_capital_usd'), int)
        assert isinstance(config.get('min_batch_size'), int)
        assert isinstance(config.get('max_batch_size'), int)
        
        # Float values
        assert isinstance(config.get('buffer_zone_tolerance_percentage'), (int, float))
        
        portfolio_config = config.get('portfolio_combination', {})
        assert isinstance(portfolio_config.get('portfolio_target_volatility'), (int, float))
    
    def test_boolean_types(self):
        """Test that boolean configuration values have correct types"""
        config = Config(FULL_CONFIG)
        
        assert isinstance(config.get('simulation_mode'), bool)
        assert isinstance(config.get('use_demo'), bool)
        assert isinstance(config.get('use_testnet'), bool)
        assert isinstance(config.get('debug_mode'), bool)
        
        portfolio_config = config.get('portfolio_combination', {})
        assert isinstance(portfolio_config.get('enable_position_netting'), bool)
    
    def test_string_types(self):
        """Test that string configuration values have correct types"""
        config = Config(FULL_CONFIG)
        
        assert isinstance(config.get('exchange'), str)
        assert isinstance(config.get('preferred_execution_style'), str)
        assert isinstance(config.get('market_index_symbol'), str)
    
    def test_dictionary_types(self):
        """Test that dictionary configuration values have correct types"""
        config = Config(FULL_CONFIG)
        
        assert isinstance(config.get('strategies'), dict)
        assert isinstance(config.get('portfolio_combination'), dict)
        assert isinstance(config.get('api_rate_limiting'), dict)
        assert isinstance(config.get('ccxt_config'), dict)


if __name__ == '__main__':
    pytest.main([__file__])
