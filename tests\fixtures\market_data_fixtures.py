"""
Market data fixtures for testing
"""

import time
from datetime import datetime

# Sample OHLCV data for testing
SAMPLE_OHLCV_DATA = {
    "BTCUSDT": [
        [1640995200000, 47000.0, 47500.0, 46500.0, 47200.0, 1234.5],  # 2022-01-01 00:00:00
        [1640998800000, 47200.0, 47800.0, 46800.0, 47600.0, 1456.7],  # 2022-01-01 01:00:00
        [1641002400000, 47600.0, 48000.0, 47100.0, 47800.0, 1678.9],  # 2022-01-01 02:00:00
        [1641006000000, 47800.0, 48200.0, 47300.0, 47900.0, 1890.1],  # 2022-01-01 03:00:00
        [1641009600000, 47900.0, 48500.0, 47400.0, 48100.0, 2012.3],  # 2022-01-01 04:00:00
    ],
    "ETHUSDT": [
        [1640995200000, 3800.0, 3850.0, 3750.0, 3820.0, 2345.6],
        [1640998800000, 3820.0, 3880.0, 3780.0, 3860.0, 2567.8],
        [1641002400000, 3860.0, 3900.0, 3810.0, 3890.0, 2789.0],
        [1641006000000, 3890.0, 3920.0, 3840.0, 3910.0, 3012.2],
        [1641009600000, 3910.0, 3950.0, 3860.0, 3930.0, 3234.4],
    ]
}

# Sample ticker data
SAMPLE_TICKER_DATA = {
    "BTCUSDT": {
        "symbol": "BTCUSDT",
        "timestamp": int(time.time() * 1000),
        "datetime": datetime.utcnow().isoformat(),
        "high": 48500.0,
        "low": 46500.0,
        "bid": 47999.5,
        "ask": 48000.5,
        "last": 48000.0,
        "close": 48000.0,
        "baseVolume": 12345.67,
        "quoteVolume": 592800000.0,
        "info": {}
    },
    "ETHUSDT": {
        "symbol": "ETHUSDT",
        "timestamp": int(time.time() * 1000),
        "datetime": datetime.utcnow().isoformat(),
        "high": 3950.0,
        "low": 3750.0,
        "bid": 3929.5,
        "ask": 3930.5,
        "last": 3930.0,
        "close": 3930.0,
        "baseVolume": 23456.78,
        "quoteVolume": 92160000.0,
        "info": {}
    }
}

# Sample funding rate data
SAMPLE_FUNDING_RATES = {
    "BTCUSDT": {
        "symbol": "BTCUSDT",
        "fundingRate": 0.0001,
        "fundingTimestamp": int(time.time() * 1000),
        "fundingDatetime": datetime.utcnow().isoformat(),
        "info": {}
    },
    "ETHUSDT": {
        "symbol": "ETHUSDT",
        "fundingRate": -0.0002,
        "fundingTimestamp": int(time.time() * 1000),
        "fundingDatetime": datetime.utcnow().isoformat(),
        "info": {}
    }
}

# Sample orderbook data
SAMPLE_ORDERBOOK_DATA = {
    "BTCUSDT": {
        "symbol": "BTCUSDT",
        "bids": [
            [47999.5, 1.234],
            [47999.0, 2.345],
            [47998.5, 3.456],
            [47998.0, 4.567],
            [47997.5, 5.678]
        ],
        "asks": [
            [48000.5, 1.234],
            [48001.0, 2.345],
            [48001.5, 3.456],
            [48002.0, 4.567],
            [48002.5, 5.678]
        ],
        "timestamp": int(time.time() * 1000),
        "datetime": datetime.utcnow().isoformat(),
        "nonce": None
    }
}

# Sample position data
SAMPLE_POSITIONS = [
    {
        "symbol": "BTCUSDT",
        "side": "long",
        "size": 0.1,
        "contracts": 0.1,
        "contractSize": 1.0,
        "entryPrice": 47000.0,
        "markPrice": 48000.0,
        "notional": 4800.0,
        "unrealizedPnl": 100.0,
        "percentage": 2.13,
        "timestamp": int(time.time() * 1000),
        "datetime": datetime.utcnow().isoformat(),
        "info": {}
    },
    {
        "symbol": "ETHUSDT",
        "side": "short",
        "size": -1.0,
        "contracts": 1.0,
        "contractSize": 1.0,
        "entryPrice": 4000.0,
        "markPrice": 3930.0,
        "notional": 3930.0,
        "unrealizedPnl": 70.0,
        "percentage": 1.75,
        "timestamp": int(time.time() * 1000),
        "datetime": datetime.utcnow().isoformat(),
        "info": {}
    }
]

# Sample markets data
SAMPLE_MARKETS = {
    "BTCUSDT": {
        "id": "BTCUSDT",
        "symbol": "BTCUSDT",
        "base": "BTC",
        "quote": "USDT",
        "active": True,
        "type": "swap",
        "spot": False,
        "margin": False,
        "future": False,
        "swap": True,
        "option": False,
        "contract": True,
        "contractSize": 1.0,
        "precision": {"amount": 8, "price": 2},
        "limits": {
            "amount": {"min": 0.001, "max": 1000000},
            "price": {"min": 0.01, "max": 1000000},
            "cost": {"min": 1.0, "max": None}
        },
        "info": {}
    },
    "ETHUSDT": {
        "id": "ETHUSDT",
        "symbol": "ETHUSDT",
        "base": "ETH",
        "quote": "USDT",
        "active": True,
        "type": "swap",
        "spot": False,
        "margin": False,
        "future": False,
        "swap": True,
        "option": False,
        "contract": True,
        "contractSize": 1.0,
        "precision": {"amount": 8, "price": 2},
        "limits": {
            "amount": {"min": 0.01, "max": 1000000},
            "price": {"min": 0.01, "max": 1000000},
            "cost": {"min": 1.0, "max": None}
        },
        "info": {}
    }
}

# Sample balance data
SAMPLE_BALANCE = {
    "USDT": {
        "free": 10000.0,
        "used": 0.0,
        "total": 10000.0
    },
    "BTC": {
        "free": 0.0,
        "used": 0.0,
        "total": 0.0
    },
    "ETH": {
        "free": 0.0,
        "used": 0.0,
        "total": 0.0
    }
}

# Sample order data
SAMPLE_ORDERS = {
    "limit_buy": {
        "id": "12345",
        "symbol": "BTCUSDT",
        "type": "limit",
        "side": "buy",
        "amount": 0.1,
        "price": 47000.0,
        "status": "open",
        "filled": 0.0,
        "remaining": 0.1,
        "timestamp": int(time.time() * 1000),
        "datetime": datetime.utcnow().isoformat(),
        "info": {}
    },
    "market_sell": {
        "id": "12346",
        "symbol": "ETHUSDT",
        "type": "market",
        "side": "sell",
        "amount": 1.0,
        "price": 3930.0,
        "status": "closed",
        "filled": 1.0,
        "remaining": 0.0,
        "timestamp": int(time.time() * 1000),
        "datetime": datetime.utcnow().isoformat(),
        "info": {}
    }
}

# Sample strategy features
SAMPLE_STRATEGY_FEATURES = {
    "stat_arb": [
        {
            "symbol": "BTCUSDT",
            "funding_rate": 0.0001,
            "annualized_funding_rate": 0.1095,
            "adjusted_funding_rate": 0.0,
            "volatility": 0.25,
            "volume_usd": 50000000.0,
            "listing_age_days": 365
        },
        {
            "symbol": "ETHUSDT",
            "funding_rate": -0.0002,
            "annualized_funding_rate": -0.219,
            "adjusted_funding_rate": -0.329,
            "volatility": 0.30,
            "volume_usd": 30000000.0,
            "listing_age_days": 300
        }
    ],
    "momentum": [
        {
            "symbol": "BTCUSDT",
            "momentum_score": 1.5,
            "z_score": 0.8,
            "volatility": 0.25,
            "volume_usd": 50000000.0,
            "market_cap_rank": 1
        },
        {
            "symbol": "ETHUSDT",
            "momentum_score": -0.5,
            "z_score": -0.3,
            "volatility": 0.30,
            "volume_usd": 30000000.0,
            "market_cap_rank": 2
        }
    ],
    "trend": [
        {
            "symbol": "BTCUSDT",
            "trend_signal": 0.6,
            "ema_short": 48000.0,
            "ema_long": 47000.0,
            "volatility": 0.25,
            "volume_usd": 50000000.0
        },
        {
            "symbol": "ETHUSDT",
            "trend_signal": -0.2,
            "ema_short": 3920.0,
            "ema_long": 3950.0,
            "volatility": 0.30,
            "volume_usd": 30000000.0
        }
    ]
}

# Sample portfolio data
SAMPLE_PORTFOLIOS = {
    "stat_arb": [
        {"symbol": "BTCUSDT", "side": "long", "size": 0.05, "price": 48000.0},
        {"symbol": "ETHUSDT", "side": "short", "size": -0.5, "price": 3930.0}
    ],
    "momentum": [
        {"symbol": "BTCUSDT", "side": "long", "size": 0.03, "price": 48000.0},
        {"symbol": "ADAUSDT", "side": "short", "size": -1000.0, "price": 0.5}
    ],
    "trend": [
        {"symbol": "SOLUSDT", "side": "long", "size": 10.0, "price": 100.0},
        {"symbol": "DOTUSDT", "side": "short", "size": -50.0, "price": 8.0}
    ]
}
