"""
End-to-end system integration tests
"""

import pytest
import asyncio
import tempfile
import yaml
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from tests.fixtures.config_fixtures import MINIMAL_CONFIG, FULL_CONFIG
from tests.helpers.mock_exchanges import MockExchange


@pytest.mark.asyncio
@pytest.mark.end_to_end
class TestSystemIntegration:
    """End-to-end system integration tests"""
    
    @pytest.fixture
    def temp_config_file(self):
        """Create temporary configuration file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(MINIMAL_CONFIG, f)
            temp_file = f.name
        
        yield temp_file
        
        # Cleanup
        Path(temp_file).unlink(missing_ok=True)
    
    async def test_config_loading_and_validation(self, temp_config_file):
        """Test configuration loading and validation"""
        from config import load_config
        from config.validation import ConfigValidator
        
        # Load configuration
        config = load_config(temp_config_file)
        assert config is not None
        
        # Validate configuration
        is_valid = ConfigValidator.validate(config.to_dict())
        assert is_valid is True
    
    async def test_exchange_factory_and_initialization(self):
        """Test exchange factory and initialization"""
        from exchanges.factory import ExchangeFactory
        
        # Create exchange
        exchange = ExchangeFactory.create_exchange('bybit')
        assert exchange is not None
        
        # Initialize with mock config
        config = MINIMAL_CONFIG.copy()
        success = await exchange.initialize(config)
        assert success is True
    
    async def test_data_fetching_pipeline(self):
        """Test complete data fetching pipeline"""
        from exchanges.factory import ExchangeFactory
        from data.fetcher import DataFetcher
        from data.cache import DataCache
        from utils.monitoring import PerformanceMonitor
        
        # Setup components
        exchange = ExchangeFactory.create_exchange('bybit')
        await exchange.initialize(MINIMAL_CONFIG)
        
        cache = DataCache(default_ttl=300)
        monitor = PerformanceMonitor()
        fetcher = DataFetcher(exchange, MINIMAL_CONFIG, cache, monitor)
        
        # Test data fetching
        symbols = ["BTCUSDT", "ETHUSDT"]
        
        for symbol in symbols:
            # Fetch ticker
            ticker = await fetcher.fetch_ticker_data(symbol)
            assert ticker is not None
            assert ticker['symbol'] == symbol
            
            # Fetch OHLCV
            ohlcv = await fetcher.fetch_ohlcv_data(symbol, "1h", 10)
            assert ohlcv is not None
            assert len(ohlcv) > 0
    
    async def test_strategy_execution_pipeline(self):
        """Test strategy execution pipeline"""
        from exchanges.factory import ExchangeFactory
        from data.fetcher import DataFetcher
        from data.analyzer import DataAnalyzer
        from data.cache import DataCache
        from utils.monitoring import PerformanceMonitor
        from strategies.base import StrategyPosition, StrategyResult
        
        # Setup components
        exchange = ExchangeFactory.create_exchange('bybit')
        await exchange.initialize(MINIMAL_CONFIG)
        
        cache = DataCache(default_ttl=300)
        monitor = PerformanceMonitor()
        fetcher = DataFetcher(exchange, MINIMAL_CONFIG, cache, monitor)
        analyzer = DataAnalyzer(exchange, MINIMAL_CONFIG)
        
        # Create a simple test strategy
        class TestStrategy:
            def __init__(self, name, config, fetcher, analyzer, exchange):
                self.strategy_name = name
                self.config = config
                self.data_fetcher = fetcher
                self.data_analyzer = analyzer
                self.exchange = exchange
            
            async def execute(self):
                # Simple strategy execution
                symbols = ["BTCUSDT", "ETHUSDT"]
                positions = []
                
                for symbol in symbols:
                    ticker = await self.data_fetcher.fetch_ticker_data(symbol)
                    if ticker:
                        position = StrategyPosition(
                            symbol=symbol,
                            side='long',
                            size=0.1,
                            price=ticker['last'],
                            confidence=0.5
                        )
                        positions.append(position)
                
                return StrategyResult(
                    strategy_name=self.strategy_name,
                    positions=positions,
                    execution_time=1.0
                )
        
        # Execute strategy
        strategy = TestStrategy('test_strategy', MINIMAL_CONFIG, fetcher, analyzer, exchange)
        result = await strategy.execute()
        
        assert isinstance(result, StrategyResult)
        assert result.strategy_name == 'test_strategy'
        assert len(result.positions) > 0
    
    async def test_portfolio_combination_pipeline(self):
        """Test portfolio combination pipeline"""
        from portfolio.combiner import PortfolioCombiner, CombinedPosition
        from strategies.base import StrategyPosition, StrategyResult
        
        # Create mock strategy results
        strategy_results = [
            StrategyResult(
                strategy_name='strategy1',
                positions=[
                    StrategyPosition('BTCUSDT', 'long', 0.1, 50000.0, 0.8, weight=0.5),
                    StrategyPosition('ETHUSDT', 'short', -1.0, 3000.0, 0.7, weight=0.5)
                ],
                execution_time=1.0
            ),
            StrategyResult(
                strategy_name='strategy2',
                positions=[
                    StrategyPosition('BTCUSDT', 'long', 0.05, 50000.0, 0.6, weight=0.3),
                    StrategyPosition('ADAUSDT', 'long', 100.0, 0.5, 0.9, weight=0.7)
                ],
                execution_time=1.5
            )
        ]
        
        # Combine portfolios
        combiner = PortfolioCombiner(FULL_CONFIG)
        combined_portfolio = combiner.combine_portfolios(strategy_results)
        
        assert isinstance(combined_portfolio, list)
        assert len(combined_portfolio) > 0
        
        for position in combined_portfolio:
            assert isinstance(position, CombinedPosition)
            assert position.symbol is not None
            assert position.net_size is not None
    
    async def test_performance_tracking_pipeline(self):
        """Test performance tracking pipeline"""
        from portfolio.basic_performance_tracker import BasicPerformanceTracker
        from portfolio.basic_metrics_database import BasicMetricsDatabase
        
        # Setup performance tracking
        config = FULL_CONFIG.copy()
        config['database'] = {'path': ':memory:'}  # Use in-memory database
        
        db = BasicMetricsDatabase(config)
        tracker = BasicPerformanceTracker(config, db)
        
        # Test portfolio update
        portfolio_positions = [
            {'symbol': 'BTCUSDT', 'side': 'long', 'size': 0.1, 'price': 50000.0},
            {'symbol': 'ETHUSDT', 'side': 'short', 'size': -1.0, 'price': 3000.0}
        ]
        
        await tracker.update_portfolio('test_strategy', 'bybit', portfolio_positions)
        
        # Test metrics calculation
        metrics = await tracker.calculate_basic_metrics('test_strategy', 'bybit')
        assert metrics is not None
    
    async def test_error_handling_throughout_system(self):
        """Test error handling throughout the system"""
        from exchanges.factory import ExchangeFactory
        from data.fetcher import DataFetcher
        from data.cache import DataCache
        from utils.monitoring import PerformanceMonitor
        
        # Setup with mock exchange that will fail
        exchange = MockExchange()
        await exchange.initialize({})
        
        # Make exchange methods fail
        exchange.fetch_ticker = lambda symbol: (_ for _ in ()).throw(Exception("Network error"))
        
        cache = DataCache(default_ttl=300)
        monitor = PerformanceMonitor()
        fetcher = DataFetcher(exchange, MINIMAL_CONFIG, cache, monitor)
        
        # Test that errors are handled gracefully
        ticker = await fetcher.fetch_ticker_data("BTCUSDT")
        assert ticker is None  # Should return None on error, not crash
    
    async def test_concurrent_operations(self):
        """Test concurrent operations throughout the system"""
        from exchanges.factory import ExchangeFactory
        from data.fetcher import DataFetcher
        from data.cache import DataCache
        from utils.monitoring import PerformanceMonitor
        
        # Setup components
        exchange = ExchangeFactory.create_exchange('bybit')
        await exchange.initialize(MINIMAL_CONFIG)
        
        cache = DataCache(default_ttl=300)
        monitor = PerformanceMonitor()
        fetcher = DataFetcher(exchange, MINIMAL_CONFIG, cache, monitor)
        
        # Test concurrent data fetching
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT"]
        
        tasks = [fetcher.fetch_ticker_data(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed or fail gracefully
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                pytest.fail(f"Concurrent operation failed for {symbols[i]}: {result}")
            else:
                assert result is not None
                assert result['symbol'] == symbols[i]
    
    async def test_memory_usage_and_cleanup(self):
        """Test memory usage and cleanup"""
        import gc
        from exchanges.factory import ExchangeFactory
        from data.cache import DataCache
        
        # Create and destroy many objects
        for _ in range(100):
            exchange = ExchangeFactory.create_exchange('bybit')
            cache = DataCache(default_ttl=300, max_size=10)
            
            # Add some data to cache
            for i in range(20):
                cache.set(f"key_{i}", f"value_{i}")
            
            # Force cleanup
            del exchange
            del cache
        
        # Force garbage collection
        gc.collect()
        
        # Test should complete without memory issues
        assert True
    
    async def test_configuration_edge_cases(self):
        """Test system behavior with edge case configurations"""
        from config import load_config
        from config.validation import ConfigValidator
        
        # Test with minimal configuration
        minimal_config = {
            'exchange': 'bybit',
            'total_capital_usd': 100,  # Very small capital
            'simulation_mode': True
        }
        
        # Should still validate
        is_valid = ConfigValidator.validate(minimal_config)
        assert is_valid is True
        
        # Test with maximum configuration values
        max_config = FULL_CONFIG.copy()
        max_config['total_capital_usd'] = 10000000  # Maximum allowed
        max_config['min_batch_size'] = 50  # Maximum allowed
        
        is_valid = ConfigValidator.validate(max_config)
        assert is_valid is True
    
    async def test_system_state_consistency(self):
        """Test that system maintains consistent state"""
        from exchanges.factory import ExchangeFactory
        from data.cache import DataCache
        
        # Create components
        exchange = ExchangeFactory.create_exchange('bybit')
        await exchange.initialize(MINIMAL_CONFIG)
        
        cache = DataCache(default_ttl=300)
        
        # Perform operations that modify state
        await exchange.fetch_ticker("BTCUSDT")
        cache.set("test_key", "test_value")
        
        # Verify state is consistent
        assert exchange.name == 'bybit'
        assert cache.get("test_key") == "test_value"
        
        # Test state after errors
        try:
            await exchange.fetch_ticker("INVALID_SYMBOL")
        except:
            pass
        
        # State should still be consistent
        assert exchange.name == 'bybit'
        assert cache.get("test_key") == "test_value"


@pytest.mark.asyncio
@pytest.mark.end_to_end
@pytest.mark.slow
class TestFullSystemWorkflow:
    """Test complete system workflow from start to finish"""
    
    async def test_complete_trading_workflow_simulation(self):
        """Test complete trading workflow in simulation mode"""
        # This test simulates a complete trading cycle
        # Note: This is a simplified version - full implementation would be much more complex
        
        from exchanges.factory import ExchangeFactory
        from data.fetcher import DataFetcher
        from data.analyzer import DataAnalyzer
        from data.cache import DataCache
        from utils.monitoring import PerformanceMonitor
        
        # 1. Setup system components
        config = FULL_CONFIG.copy()
        config['simulation_mode'] = True
        
        exchange = ExchangeFactory.create_exchange('bybit')
        await exchange.initialize(config)
        
        cache = DataCache(default_ttl=300)
        monitor = PerformanceMonitor()
        fetcher = DataFetcher(exchange, config, cache, monitor)
        analyzer = DataAnalyzer(exchange, config)
        
        # 2. Fetch market data
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        market_data = {}
        
        for symbol in symbols:
            ticker = await fetcher.fetch_ticker_data(symbol)
            ohlcv = await fetcher.fetch_ohlcv_data(symbol, "1h", 24)
            
            market_data[symbol] = {
                'ticker': ticker,
                'ohlcv': ohlcv
            }
        
        # 3. Analyze data and generate signals
        signals = {}
        for symbol, data in market_data.items():
            if data['ohlcv']:
                volatility = analyzer.calculate_volatility_from_ohlcv(data['ohlcv'])
                signals[symbol] = {
                    'volatility': volatility,
                    'price': data['ticker']['last'] if data['ticker'] else 0
                }
        
        # 4. Verify workflow completed successfully
        assert len(market_data) == len(symbols)
        assert len(signals) > 0
        
        # All components should be functioning
        assert exchange.name == 'bybit'
        assert cache.get_stats()['size'] > 0  # Cache should have data
        assert monitor.get_stats()['counters']  # Monitor should have recorded activity


if __name__ == '__main__':
    pytest.main([__file__, '-v', '-m', 'end_to_end'])
