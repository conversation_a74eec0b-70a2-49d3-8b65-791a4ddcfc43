"""
Unit tests for data fetcher module
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from data.fetcher import DataFetcher
from data.cache import DataCache
from utils.monitoring import PerformanceMonitor
from tests.helpers.mock_exchanges import MockExchange
from tests.fixtures.market_data_fixtures import (
    SAMPLE_OHLCV_DATA, SAMPLE_TICKER_DATA, SAMPLE_FUNDING_RATES
)


class TestDataFetcher:
    """Test DataFetcher class"""
    
    @pytest.fixture
    def mock_exchange(self):
        """Create mock exchange for testing"""
        return MockExchange()
    
    @pytest.fixture
    def mock_config(self):
        """Create mock configuration"""
        return {
            'cache_default_ttl': 300,
            'cache_max_size': 1000,
            'cache_gc_interval': 300,
            'max_concurrent_api_calls': 5,
            'rate_limit_delay_ms': 100
        }
    
    @pytest.fixture
    def data_cache(self):
        """Create data cache for testing"""
        return DataCache(default_ttl=300, max_size=100)
    
    @pytest.fixture
    def performance_monitor(self):
        """Create performance monitor for testing"""
        return PerformanceMonitor()
    
    @pytest.fixture
    async def data_fetcher(self, mock_exchange, mock_config, data_cache, performance_monitor):
        """Create DataFetcher instance for testing"""
        await mock_exchange.initialize({})
        return DataFetcher(mock_exchange, mock_config, data_cache, performance_monitor)
    
    @pytest.mark.asyncio
    async def test_fetch_ohlcv_data_success(self, data_fetcher):
        """Test successful OHLCV data fetching"""
        symbol = "BTCUSDT"
        timeframe = "1h"
        limit = 100
        
        result = await data_fetcher.fetch_ohlcv_data(symbol, timeframe, limit)
        
        assert result is not None
        assert isinstance(result, list)
        assert len(result) > 0
        
        # Validate OHLCV structure
        for candle in result:
            assert len(candle) >= 6  # timestamp, open, high, low, close, volume
            assert all(isinstance(x, (int, float)) for x in candle)
    
    @pytest.mark.asyncio
    async def test_fetch_ohlcv_data_caching(self, data_fetcher):
        """Test OHLCV data caching functionality"""
        symbol = "BTCUSDT"
        timeframe = "1h"
        limit = 100
        
        # First fetch - should hit exchange
        result1 = await data_fetcher.fetch_ohlcv_data(symbol, timeframe, limit)
        
        # Second fetch - should hit cache
        result2 = await data_fetcher.fetch_ohlcv_data(symbol, timeframe, limit)
        
        assert result1 == result2
        
        # Verify cache hit
        cache_stats = data_fetcher.cache.get_stats()
        assert cache_stats['hits'] > 0
    
    @pytest.mark.asyncio
    async def test_fetch_ticker_data_success(self, data_fetcher):
        """Test successful ticker data fetching"""
        symbol = "BTCUSDT"
        
        result = await data_fetcher.fetch_ticker_data(symbol)
        
        assert result is not None
        assert isinstance(result, dict)
        assert 'symbol' in result
        assert 'last' in result
        assert 'bid' in result
        assert 'ask' in result
        assert result['symbol'] == symbol
        assert result['last'] > 0
    
    @pytest.mark.asyncio
    async def test_fetch_funding_rate_success(self, data_fetcher):
        """Test successful funding rate fetching"""
        symbol = "BTCUSDT"
        
        result = await data_fetcher.fetch_funding_rate(symbol)
        
        assert result is not None
        assert isinstance(result, dict)
        assert 'symbol' in result
        assert 'fundingRate' in result
        assert result['symbol'] == symbol
        assert isinstance(result['fundingRate'], (int, float))
    
    @pytest.mark.asyncio
    async def test_fetch_multiple_symbols_parallel(self, data_fetcher):
        """Test fetching data for multiple symbols in parallel"""
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        
        # Fetch tickers for all symbols
        tasks = [data_fetcher.fetch_ticker_data(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks)
        
        assert len(results) == len(symbols)
        
        for i, result in enumerate(results):
            assert result is not None
            assert result['symbol'] == symbols[i]
    
    @pytest.mark.asyncio
    async def test_fetch_ohlcv_invalid_symbol(self, data_fetcher):
        """Test OHLCV fetching with invalid symbol"""
        # Mock exchange to raise exception for invalid symbol
        data_fetcher.exchange.fetch_ohlcv = AsyncMock(side_effect=Exception("Invalid symbol"))
        
        result = await data_fetcher.fetch_ohlcv_data("INVALID_SYMBOL", "1h", 100)
        
        # Should return None on error
        assert result is None
    
    @pytest.mark.asyncio
    async def test_fetch_ticker_invalid_symbol(self, data_fetcher):
        """Test ticker fetching with invalid symbol"""
        # Mock exchange to raise exception for invalid symbol
        data_fetcher.exchange.fetch_ticker = AsyncMock(side_effect=Exception("Invalid symbol"))
        
        result = await data_fetcher.fetch_ticker_data("INVALID_SYMBOL")
        
        # Should return None on error
        assert result is None
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, data_fetcher):
        """Test that rate limiting is applied"""
        import time
        
        symbol = "BTCUSDT"
        
        # Make multiple rapid requests
        start_time = time.time()
        
        tasks = [data_fetcher.fetch_ticker_data(symbol) for _ in range(3)]
        await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should take some time due to rate limiting
        assert total_time > 0.1  # At least 100ms for rate limiting
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self, data_fetcher):
        """Test cache expiration functionality"""
        symbol = "BTCUSDT"
        
        # Set very short TTL for testing
        data_fetcher.cache.default_ttl = 0.1  # 100ms
        
        # First fetch
        result1 = await data_fetcher.fetch_ticker_data(symbol)
        
        # Wait for cache to expire
        await asyncio.sleep(0.2)
        
        # Second fetch - should hit exchange again
        result2 = await data_fetcher.fetch_ticker_data(symbol)
        
        # Results might be different due to mock data generation
        assert result1 is not None
        assert result2 is not None
    
    @pytest.mark.asyncio
    async def test_concurrent_api_calls_limit(self, data_fetcher):
        """Test concurrent API calls are limited"""
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT", "DOTUSDT", "LINKUSDT"]
        
        # Create many concurrent requests
        tasks = [data_fetcher.fetch_ticker_data(symbol) for symbol in symbols]
        
        # Should complete without errors despite semaphore limiting
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All requests should succeed
        for result in results:
            assert not isinstance(result, Exception)
            assert result is not None
    
    @pytest.mark.asyncio
    async def test_data_validation(self, data_fetcher):
        """Test data validation in fetcher"""
        symbol = "BTCUSDT"
        
        # Test OHLCV data validation
        ohlcv_result = await data_fetcher.fetch_ohlcv_data(symbol, "1h", 10)
        
        if ohlcv_result:
            for candle in ohlcv_result:
                # Validate OHLCV structure
                assert len(candle) >= 6
                timestamp, open_price, high, low, close, volume = candle[:6]
                
                # Validate price relationships
                assert high >= max(open_price, close)
                assert low <= min(open_price, close)
                assert all(price > 0 for price in [open_price, high, low, close])
                assert volume >= 0
    
    @pytest.mark.asyncio
    async def test_error_recovery(self, data_fetcher):
        """Test error recovery mechanisms"""
        symbol = "BTCUSDT"
        
        # Mock exchange to fail first time, succeed second time
        call_count = 0
        
        async def mock_fetch_ticker(symbol):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("Network error")
            return SAMPLE_TICKER_DATA[symbol]
        
        data_fetcher.exchange.fetch_ticker = mock_fetch_ticker
        
        # Should recover from first failure
        result = await data_fetcher.fetch_ticker_data(symbol)
        
        # Depending on retry logic, might succeed or fail
        # This tests that the error handling doesn't crash the system
        assert call_count >= 1
    
    @pytest.mark.asyncio
    async def test_performance_monitoring(self, data_fetcher):
        """Test performance monitoring integration"""
        symbol = "BTCUSDT"
        
        # Fetch some data
        await data_fetcher.fetch_ticker_data(symbol)
        await data_fetcher.fetch_ohlcv_data(symbol, "1h", 10)
        
        # Check that performance metrics were recorded
        stats = data_fetcher.monitor.get_stats()
        
        # Should have recorded some API calls
        assert 'counters' in stats
        assert 'timings' in stats
    
    @pytest.mark.asyncio
    async def test_fetch_multiple_timeframes(self, data_fetcher):
        """Test fetching multiple timeframes for same symbol"""
        symbol = "BTCUSDT"
        timeframes = ["1h", "4h", "1d"]
        
        results = {}
        for timeframe in timeframes:
            result = await data_fetcher.fetch_ohlcv_data(symbol, timeframe, 10)
            results[timeframe] = result
        
        # All timeframes should return data
        for timeframe, result in results.items():
            assert result is not None, f"No data for {timeframe}"
            assert len(result) > 0, f"Empty data for {timeframe}"
    
    @pytest.mark.asyncio
    async def test_cache_key_generation(self, data_fetcher):
        """Test cache key generation for different requests"""
        symbol = "BTCUSDT"
        
        # Fetch same data twice
        await data_fetcher.fetch_ohlcv_data(symbol, "1h", 100)
        await data_fetcher.fetch_ohlcv_data(symbol, "1h", 100)
        
        # Fetch different timeframe
        await data_fetcher.fetch_ohlcv_data(symbol, "4h", 100)
        
        # Check cache has correct number of entries
        cache_stats = data_fetcher.cache.get_stats()
        assert cache_stats['size'] >= 2  # At least 2 different cache keys


if __name__ == '__main__':
    pytest.main([__file__])
