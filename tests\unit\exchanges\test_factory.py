"""
Unit tests for exchange factory module
"""

import pytest
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from exchanges.factory import ExchangeFactory
from exchanges.base import ExchangeInterface
from exchanges.bybit import BybitExchange
from exchanges.binance import BinanceExchange
from exchanges.okx import OKXExchange
from exchanges.hyperliquid import HyperliquidExchange


class TestExchangeFactory:
    """Test ExchangeFactory class"""
    
    def test_create_bybit_exchange(self):
        """Test creating Bybit exchange"""
        exchange = ExchangeFactory.create_exchange('bybit')
        
        assert isinstance(exchange, BybitExchange)
        assert isinstance(exchange, ExchangeInterface)
        assert exchange.name == 'bybit'
    
    def test_create_binance_exchange(self):
        """Test creating Binance exchange"""
        exchange = ExchangeFactory.create_exchange('binance')
        
        assert isinstance(exchange, BinanceExchange)
        assert isinstance(exchange, ExchangeInterface)
        assert exchange.name == 'binance'
    
    def test_create_okx_exchange(self):
        """Test creating OKX exchange"""
        exchange = ExchangeFactory.create_exchange('okx')
        
        assert isinstance(exchange, OKXExchange)
        assert isinstance(exchange, ExchangeInterface)
        assert exchange.name == 'okx'
    
    def test_create_hyperliquid_exchange(self):
        """Test creating Hyperliquid exchange"""
        exchange = ExchangeFactory.create_exchange('hyperliquid')
        
        assert isinstance(exchange, HyperliquidExchange)
        assert isinstance(exchange, ExchangeInterface)
        assert exchange.name == 'hyperliquid'
    
    def test_create_exchange_case_insensitive(self):
        """Test exchange creation is case insensitive"""
        # Test uppercase
        exchange_upper = ExchangeFactory.create_exchange('BYBIT')
        assert isinstance(exchange_upper, BybitExchange)
        
        # Test mixed case
        exchange_mixed = ExchangeFactory.create_exchange('ByBiT')
        assert isinstance(exchange_mixed, BybitExchange)
        
        # Test with spaces (should be handled by caller, but test anyway)
        exchange_spaces = ExchangeFactory.create_exchange(' bybit ')
        assert isinstance(exchange_spaces, BybitExchange)
    
    def test_create_unsupported_exchange(self):
        """Test creating unsupported exchange raises error"""
        with pytest.raises(ValueError, match="Unsupported exchange"):
            ExchangeFactory.create_exchange('unsupported_exchange')
        
        with pytest.raises(ValueError, match="Unsupported exchange"):
            ExchangeFactory.create_exchange('coinbase')
        
        with pytest.raises(ValueError, match="Unsupported exchange"):
            ExchangeFactory.create_exchange('')
    
    def test_get_supported_exchanges(self):
        """Test getting list of supported exchanges"""
        supported = ExchangeFactory.get_supported_exchanges()
        
        assert isinstance(supported, list)
        assert len(supported) > 0
        
        # Check all expected exchanges are supported
        expected_exchanges = ['bybit', 'binance', 'okx', 'hyperliquid']
        for exchange in expected_exchanges:
            assert exchange in supported
    
    def test_all_supported_exchanges_can_be_created(self):
        """Test that all supported exchanges can be created"""
        supported = ExchangeFactory.get_supported_exchanges()
        
        for exchange_name in supported:
            exchange = ExchangeFactory.create_exchange(exchange_name)
            assert isinstance(exchange, ExchangeInterface)
            assert exchange.name == exchange_name
    
    def test_exchange_interface_compliance(self):
        """Test that all created exchanges comply with ExchangeInterface"""
        supported = ExchangeFactory.get_supported_exchanges()
        
        for exchange_name in supported:
            exchange = ExchangeFactory.create_exchange(exchange_name)
            
            # Check required methods exist
            required_methods = [
                'initialize', 'fetch_markets', 'fetch_tickers', 'fetch_ticker',
                'fetch_ohlcv', 'fetch_funding_rate', 'fetch_positions',
                'fetch_balance', 'fetch_order_book', 'create_limit_order',
                'create_market_order', 'cancel_order'
            ]
            
            for method_name in required_methods:
                assert hasattr(exchange, method_name), f"{exchange_name} missing {method_name}"
                assert callable(getattr(exchange, method_name)), f"{exchange_name}.{method_name} not callable"
            
            # Check name property
            assert hasattr(exchange, 'name')
            assert isinstance(exchange.name, str)
    
    def test_factory_is_stateless(self):
        """Test that factory doesn't maintain state between calls"""
        # Create multiple instances of same exchange
        exchange1 = ExchangeFactory.create_exchange('bybit')
        exchange2 = ExchangeFactory.create_exchange('bybit')
        
        # Should be different instances
        assert exchange1 is not exchange2
        assert type(exchange1) == type(exchange2)
    
    def test_factory_error_messages(self):
        """Test that factory provides helpful error messages"""
        try:
            ExchangeFactory.create_exchange('invalid')
        except ValueError as e:
            error_message = str(e)
            
            # Should mention the invalid exchange name
            assert 'invalid' in error_message
            
            # Should list supported exchanges
            supported = ExchangeFactory.get_supported_exchanges()
            for exchange in supported:
                assert exchange in error_message
    
    def test_exchange_name_property_consistency(self):
        """Test that exchange name property matches factory input"""
        test_cases = [
            ('bybit', 'bybit'),
            ('BINANCE', 'binance'),  # Should normalize to lowercase
            ('OkX', 'okx'),
            ('HyperLiquid', 'hyperliquid')
        ]
        
        for input_name, expected_name in test_cases:
            exchange = ExchangeFactory.create_exchange(input_name)
            assert exchange.name == expected_name
    
    def test_factory_with_none_input(self):
        """Test factory behavior with None input"""
        with pytest.raises((ValueError, TypeError)):
            ExchangeFactory.create_exchange(None)
    
    def test_factory_with_numeric_input(self):
        """Test factory behavior with numeric input"""
        with pytest.raises((ValueError, TypeError)):
            ExchangeFactory.create_exchange(123)
    
    def test_factory_with_empty_string(self):
        """Test factory behavior with empty string"""
        with pytest.raises(ValueError, match="Unsupported exchange"):
            ExchangeFactory.create_exchange('')
    
    def test_multiple_exchange_types_simultaneously(self):
        """Test creating multiple different exchange types"""
        exchanges = {}
        
        for exchange_name in ExchangeFactory.get_supported_exchanges():
            exchanges[exchange_name] = ExchangeFactory.create_exchange(exchange_name)
        
        # Verify all are different types
        exchange_types = set(type(exchange) for exchange in exchanges.values())
        assert len(exchange_types) == len(exchanges)
        
        # Verify all implement the interface
        for exchange in exchanges.values():
            assert isinstance(exchange, ExchangeInterface)
    
    def test_factory_thread_safety(self):
        """Test factory can be used safely from multiple threads"""
        import threading
        import time
        
        results = []
        errors = []
        
        def create_exchange_worker():
            try:
                exchange = ExchangeFactory.create_exchange('bybit')
                results.append(exchange)
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=create_exchange_worker)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        assert len(errors) == 0, f"Errors occurred: {errors}"
        assert len(results) == 10
        
        # All should be BybitExchange instances but different objects
        for exchange in results:
            assert isinstance(exchange, BybitExchange)
        
        # Should be different instances
        exchange_ids = set(id(exchange) for exchange in results)
        assert len(exchange_ids) == 10


class TestExchangeFactoryIntegration:
    """Integration tests for ExchangeFactory"""
    
    def test_factory_with_real_exchange_initialization(self):
        """Test factory-created exchanges can be initialized"""
        # This test doesn't actually connect to exchanges but tests the initialization interface
        
        for exchange_name in ExchangeFactory.get_supported_exchanges():
            exchange = ExchangeFactory.create_exchange(exchange_name)
            
            # Test that initialize method exists and can be called
            # (We don't actually initialize to avoid network calls in unit tests)
            assert hasattr(exchange, 'initialize')
            assert callable(exchange.initialize)
    
    def test_factory_exchange_configuration_compatibility(self):
        """Test that factory-created exchanges work with different configurations"""
        from tests.fixtures.config_fixtures import EXCHANGE_CONFIGS
        
        for exchange_name, config in EXCHANGE_CONFIGS.items():
            if exchange_name in ExchangeFactory.get_supported_exchanges():
                exchange = ExchangeFactory.create_exchange(exchange_name)
                
                # Test that the exchange can handle the configuration structure
                # (We don't actually initialize to avoid network calls)
                assert exchange.name == exchange_name
                
                # Verify config has expected structure for this exchange
                assert 'exchange' in config
                assert config['exchange'] == exchange_name


if __name__ == '__main__':
    pytest.main([__file__])
