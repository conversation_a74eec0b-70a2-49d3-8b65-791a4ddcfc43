"""
Unit tests for configuration validation module
"""

import pytest
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from config.validation import ConfigValida<PERSON>
from tests.fixtures.config_fixtures import (
    MINIMAL_CONFIG, FULL_CONFIG, INVALID_CONFIGS,
    STRATEGY_CONFIGS, EXCHANGE_CONFIGS
)


class TestConfigValidator:
    """Test ConfigValidator class"""
    
    def test_validate_minimal_config(self):
        """Test validation of minimal valid configuration"""
        result = ConfigValidator.validate(MINIMAL_CONFIG)
        assert result is True
    
    def test_validate_full_config(self):
        """Test validation of full configuration"""
        result = ConfigValidator.validate(FULL_CONFIG)
        assert result is True
    
    def test_validate_missing_exchange(self):
        """Test validation fails for missing exchange"""
        with pytest.raises(ValueError, match="Required config field missing: exchange"):
            ConfigValidator.validate(INVALID_CONFIGS["missing_exchange"])
    
    def test_validate_invalid_exchange(self):
        """Test validation fails for invalid exchange"""
        with pytest.raises(ValueError, match="Unsupported exchange"):
            ConfigValidator.validate(INVALID_CONFIGS["invalid_exchange"])
    
    def test_validate_negative_capital(self):
        """Test validation fails for negative capital"""
        with pytest.raises(ValueError, match="Total capital must be between"):
            ConfigValidator.validate(INVALID_CONFIGS["negative_capital"])
    
    def test_validate_invalid_strategy_weight(self):
        """Test validation fails for invalid strategy weight"""
        with pytest.raises(ValueError, match="Strategy weight must be between"):
            ConfigValidator.validate(INVALID_CONFIGS["invalid_strategy_weight"])
    
    def test_validate_invalid_volatility(self):
        """Test validation fails for invalid volatility"""
        with pytest.raises(ValueError, match="Target volatility must be between"):
            ConfigValidator.validate(INVALID_CONFIGS["invalid_volatility"])
    
    def test_validate_supported_exchanges(self):
        """Test validation of all supported exchanges"""
        supported_exchanges = ['bybit', 'binance', 'okx', 'hyperliquid']
        
        for exchange in supported_exchanges:
            config = MINIMAL_CONFIG.copy()
            config['exchange'] = exchange
            
            result = ConfigValidator.validate(config)
            assert result is True
    
    def test_validate_strategy_configurations(self):
        """Test validation of strategy-specific configurations"""
        base_config = MINIMAL_CONFIG.copy()
        
        for strategy_name, strategy_config in STRATEGY_CONFIGS.items():
            test_config = base_config.copy()
            test_config['strategies'] = {strategy_name: strategy_config}
            
            result = ConfigValidator.validate(test_config)
            assert result is True
    
    def test_validate_exchange_configurations(self):
        """Test validation of exchange-specific configurations"""
        for exchange_name, exchange_config in EXCHANGE_CONFIGS.items():
            test_config = MINIMAL_CONFIG.copy()
            test_config.update(exchange_config)
            
            result = ConfigValidator.validate(test_config)
            assert result is True
    
    def test_validate_numeric_ranges(self):
        """Test validation of numeric parameter ranges"""
        base_config = MINIMAL_CONFIG.copy()
        
        # Test valid ranges
        valid_params = {
            'total_capital_usd': 1000,
            'min_daily_volume_usd': 1000000,
            'max_abs_funding_rate': 0.001,
            'target_volatility': 0.2,
            'min_batch_size': 3,
            'max_batch_size': 10,
            'min_batch_interval_seconds': 60,
            'max_batch_interval_seconds': 300
        }
        
        for param, value in valid_params.items():
            test_config = base_config.copy()
            test_config[param] = value
            
            result = ConfigValidator.validate(test_config)
            assert result is True
        
        # Test invalid ranges
        invalid_params = {
            'total_capital_usd': -1000,  # Negative
            'min_daily_volume_usd': 0,   # Too small
            'max_abs_funding_rate': 1.0, # Too large
            'target_volatility': -0.1,   # Negative
            'min_batch_size': 0,         # Too small
            'max_batch_size': 100,       # Too large
            'min_batch_interval_seconds': 0,    # Too small
            'max_batch_interval_seconds': 10000 # Too large
        }
        
        for param, value in invalid_params.items():
            test_config = base_config.copy()
            test_config[param] = value
            
            with pytest.raises(ValueError):
                ConfigValidator.validate(test_config)
    
    def test_validate_trading_cost_adjustment(self):
        """Test validation of trading cost adjustment parameter"""
        base_config = MINIMAL_CONFIG.copy()
        
        # Test valid float value
        test_config = base_config.copy()
        test_config['trading_cost_adjustment'] = 0.1095
        result = ConfigValidator.validate(test_config)
        assert result is True
        
        # Test valid dictionary value
        test_config = base_config.copy()
        test_config['trading_cost_adjustment'] = {
            'bybit': 0.0001,
            'binance': 0.0001,
            'okx': 0.0001,
            'hyperliquid': 0.0000125
        }
        result = ConfigValidator.validate(test_config)
        assert result is True
        
        # Test invalid negative value
        test_config = base_config.copy()
        test_config['trading_cost_adjustment'] = -0.1
        with pytest.raises(ValueError, match="Trading cost adjustment must be between 0% and 50%"):
            ConfigValidator.validate(test_config)
    
    def test_validate_strategy_weights(self):
        """Test validation of strategy weights"""
        base_config = MINIMAL_CONFIG.copy()
        
        # Test valid weights
        valid_strategies = {
            'stat_arb_carry_trade': {'enabled': True, 'weight': 0.5},
            'cross_sectional_momentum': {'enabled': True, 'weight': 0.3},
            'trend_trading': {'enabled': True, 'weight': 0.2}
        }
        
        test_config = base_config.copy()
        test_config['strategies'] = valid_strategies
        result = ConfigValidator.validate(test_config)
        assert result is True
        
        # Test negative weight
        invalid_strategies = {
            'stat_arb_carry_trade': {'enabled': True, 'weight': -0.5}
        }
        
        test_config = base_config.copy()
        test_config['strategies'] = invalid_strategies
        with pytest.raises(ValueError, match="Strategy weight must be between 0 and 1"):
            ConfigValidator.validate(test_config)

        # Test weight too large
        invalid_strategies = {
            'stat_arb_carry_trade': {'enabled': True, 'weight': 2.0}
        }

        test_config = base_config.copy()
        test_config['strategies'] = invalid_strategies
        with pytest.raises(ValueError, match="Strategy weight must be between 0 and 1"):
            ConfigValidator.validate(test_config)
    
    def test_validate_portfolio_combination_settings(self):
        """Test validation of portfolio combination settings"""
        base_config = MINIMAL_CONFIG.copy()
        
        # Test valid portfolio combination settings
        valid_portfolio_config = {
            'enable_position_netting': True,
            'min_position_size_usd': 10.0,
            'position_rounding_decimals': 6,
            'enable_portfolio_vol_targeting': True,
            'portfolio_target_volatility': 0.20,
            'default_correlation': 0.30
        }
        
        test_config = base_config.copy()
        test_config['portfolio_combination'] = valid_portfolio_config
        result = ConfigValidator.validate(test_config)
        assert result is True
        
        # Test invalid volatility target
        invalid_portfolio_config = {
            'portfolio_target_volatility': -0.1
        }
        
        test_config = base_config.copy()
        test_config['portfolio_combination'] = invalid_portfolio_config
        with pytest.raises(ValueError, match="Target volatility must be between 1% and 200%"):
            ConfigValidator.validate(test_config)
    
    def test_validate_api_rate_limiting_settings(self):
        """Test validation of API rate limiting settings"""
        base_config = MINIMAL_CONFIG.copy()
        
        # Test valid rate limiting settings
        valid_rate_config = {
            'max_requests_per_second': 5,
            'max_concurrent_requests': 3,
            'burst_limit': 10,
            'order_placement': {
                'max_orders_per_second': 3,
                'max_orders_per_minute': 120,
                'batch_delay_ms': 500,
                'retry_delay_ms': 1500,
                'max_retries': 3
            }
        }
        
        test_config = base_config.copy()
        test_config['api_rate_limiting'] = valid_rate_config
        result = ConfigValidator.validate(test_config)
        assert result is True
    
    def test_validate_execution_settings(self):
        """Test validation of execution settings"""
        base_config = MINIMAL_CONFIG.copy()
        
        # Test valid execution settings
        valid_execution_params = {
            'min_batch_size': 3,
            'max_batch_size': 5,
            'min_batch_interval_seconds': 60,
            'max_batch_interval_seconds': 300,
            'min_orderbook_levels': 3,
            'max_orderbook_levels': 10,
            'max_execution_iterations': 8,
            'order_settle_time_seconds': 300
        }
        
        for param, value in valid_execution_params.items():
            test_config = base_config.copy()
            test_config[param] = value
            
            result = ConfigValidator.validate(test_config)
            assert result is True
    
    def test_validate_data_settings(self):
        """Test validation of data-related settings"""
        base_config = MINIMAL_CONFIG.copy()
        
        # Test valid data settings
        valid_data_params = {
            'exclude_new_listings_days': 60,
            'min_historical_data_days': 60,
            'min_orderbook_depth': 3,
            'max_spread_threshold': 0.05,
            'cache_default_ttl': 300,
            'cache_max_size': 1000
        }
        
        for param, value in valid_data_params.items():
            test_config = base_config.copy()
            test_config[param] = value
            
            result = ConfigValidator.validate(test_config)
            assert result is True
    
    def test_validate_boolean_settings(self):
        """Test validation of boolean settings"""
        base_config = MINIMAL_CONFIG.copy()
        
        # Test valid boolean settings
        boolean_params = [
            'simulation_mode', 'use_demo', 'use_testnet', 'debug_mode',
            'immediate_start', 'enable_beta_projection', 'monitor_position_alignment'
        ]
        
        for param in boolean_params:
            for value in [True, False]:
                test_config = base_config.copy()
                test_config[param] = value
                
                result = ConfigValidator.validate(test_config)
                assert result is True
    
    def test_validate_string_settings(self):
        """Test validation of string settings"""
        base_config = MINIMAL_CONFIG.copy()
        
        # Test valid string settings
        valid_string_params = {
            'exchange': 'bybit',
            'preferred_execution_style': 'conservative',
            'market_index_symbol': 'BTCUSDT'
        }
        
        for param, value in valid_string_params.items():
            test_config = base_config.copy()
            test_config[param] = value
            
            result = ConfigValidator.validate(test_config)
            assert result is True


class TestConfigValidationEdgeCases:
    """Test edge cases in configuration validation"""
    
    def test_validate_empty_config(self):
        """Test validation of empty configuration"""
        with pytest.raises(ValueError):
            ConfigValidator.validate({})
    
    def test_validate_none_config(self):
        """Test validation of None configuration"""
        with pytest.raises((ValueError, TypeError)):
            ConfigValidator.validate(None)
    
    def test_validate_config_with_extra_fields(self):
        """Test validation allows extra fields"""
        config = MINIMAL_CONFIG.copy()
        config['extra_field'] = 'extra_value'
        config['another_extra'] = 123
        
        result = ConfigValidator.validate(config)
        assert result is True
    
    def test_validate_config_with_nested_extra_fields(self):
        """Test validation allows extra nested fields"""
        config = FULL_CONFIG.copy()
        config['strategies']['stat_arb_carry_trade']['extra_param'] = 'extra_value'
        config['new_section'] = {'new_param': 'new_value'}
        
        result = ConfigValidator.validate(config)
        assert result is True
    
    def test_validate_config_boundary_values(self):
        """Test validation with boundary values"""
        base_config = MINIMAL_CONFIG.copy()
        
        # Test minimum boundary values
        boundary_params = {
            'total_capital_usd': 100,           # Minimum allowed
            'min_daily_volume_usd': 1000,       # Minimum allowed
            'max_abs_funding_rate': 0.0001,     # Minimum allowed
            'target_volatility': 0.01,          # Minimum allowed
            'min_batch_size': 1,                # Minimum allowed
            'max_batch_size': 1,                # Minimum allowed
        }
        
        for param, value in boundary_params.items():
            test_config = base_config.copy()
            test_config[param] = value
            
            result = ConfigValidator.validate(test_config)
            assert result is True
        
        # Test maximum boundary values
        max_boundary_params = {
            'total_capital_usd': 10000000,      # Maximum allowed
            'min_daily_volume_usd': 1000000000, # Maximum allowed
            'max_abs_funding_rate': 0.1,        # Maximum allowed
            'target_volatility': 2.0,           # Maximum allowed
            'max_batch_size': 50,               # Maximum allowed
        }
        
        for param, value in max_boundary_params.items():
            test_config = base_config.copy()
            test_config[param] = value
            
            result = ConfigValidator.validate(test_config)
            assert result is True


if __name__ == '__main__':
    pytest.main([__file__])
