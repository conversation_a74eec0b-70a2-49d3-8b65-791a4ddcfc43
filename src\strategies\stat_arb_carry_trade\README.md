# Statistical Arbitrage Carry Trade Strategy

A funding arbitrage strategy that exploits funding rate differentials in cryptocurrency perpetual futures markets by taking long positions in assets with negative funding rates and short positions in assets with positive funding rates.

## Table of Contents

1. [Strategy Overview](#strategy-overview)
2. [Universe Selection](#universe-selection)
3. [Feature Engineering](#feature-engineering)
4. [Model](#model)
5. [Position Selection](#position-selection)
6. [Position Sizing](#position-sizing)
7. [Target Portfolio Generation](#target-portfolio-generation)
8. [Configuration Parameters](#configuration-parameters)
9. [Risk Management](#risk-management)
10. [Performance Characteristics](#performance-characteristics)

## Strategy Overview

The Statistical Arbitrage Carry Trade strategy captures funding payments in cryptocurrency perpetual futures markets while maintaining market neutrality. The strategy identifies assets with attractive funding rate profiles and constructs a portfolio that benefits from funding rate convergence.

**Core Principle**: Funding rates in perpetual futures markets fluctuate around zero. When funding rates deviate significantly from zero, they tend to revert, providing profit opportunities for carry trades.

## Universe Selection

### Selection Criteria
The strategy selects from perpetual futures contracts based on:

1. **Volume Filter**: Minimum 5-day average volume of $1,000,000 USD
2. **Volatility Filter**: Minimum volatility of 5% to exclude stablecoins
3. **Listing Age**: Exclude coins listed within the last 90 days (increased from 60 days)
4. **Data Availability**: Minimum 90 days of historical data required (increased from 60 days)

**Simplified**: Removed funding rate bounds filter for maximum simplicity

### Implementation
```python
async def get_universe(self) -> List[str]:
    # Create strategy-specific config with 90-day requirements
    strategy_config = self.data_fetcher.config.copy()
    strategy_config.update({
        'exclude_new_listings_days': 90,  # 90 days minimum listing age
        'min_historical_data_days': 90,   # 90 days minimum historical data
        'min_daily_volume_usd': 1000000,
        'min_volatility_threshold': 0.05
    })

    # Apply strategy-specific configuration
    original_config = self.data_fetcher.config
    self.data_fetcher.config = strategy_config

    try:
        eligible_coins = await self.data_fetcher.get_eligible_coins()
    finally:
        self.data_fetcher.config = original_config

    return [coin['symbol'] for coin in eligible_coins]
```

### Universe Characteristics
- **Size**: Typically 50-150 symbols depending on market conditions
- **Composition**: Major cryptocurrencies with sufficient liquidity
- **Exclusions**: Stablecoins, new listings, low-volume assets
- **Updates**: Universe refreshed daily during rebalancing

## Feature Engineering

### Primary Features

#### 1. Annualized Funding Rate (Simplified)
All funding rates are converted to annualized form:
```python
# Annualization factors:
# Bybit, Binance, OKX: 8-hour funding → multiply by 1095 (365*24/8)
# Hyperliquid: 1-hour funding → multiply by 8760 (365*24/1)
annualized_funding = funding_rate_3d_mean * annualization_factor
```

#### 2. Adjusted Annualized Funding Rate
The core signal combining annualized funding rate with trading costs:
```python
adjusted_funding = annualized_funding - 10.95%  # 10.95% annualized trading cost
```

**Components**:
- **3-Day Mean Funding Rate**: Smoothed funding rate to reduce noise, then annualized
- **Trading Cost Adjustment**: Fixed 10.95% annualized cost for all exchanges
- **Floor Protection**: Prevents sign flips (positive rates can't go negative, negative rates can't go positive)

#### 2. Volatility Metrics
Weighted volatility calculation for risk management:
```python
weighted_volatility = (
    0.3 * volatility_60d +
    0.5 * volatility_30d +
    0.2 * volatility_10d
)
```

#### 3. No Beta Calculation Required
Beta calculation is disabled for this strategy:
- Individual volatility targeting provides sufficient risk management
- Linear decay weights ensure balanced portfolio allocation
- No additional market neutrality calculations needed

### Feature Validation
- **Outlier Detection**: Remove extreme funding rate values
- **Data Quality Checks**: Validate completeness and consistency
- **Normalization**: Ensure features are properly scaled

## Model

### Expected Value Framework
The strategy uses an Expected Value (EV) based approach:

```python
EV = adjusted_funding_rate * holding_period - transaction_costs
```

**Key Assumptions**:
- Funding rates mean-revert over the holding period
- Transaction costs are predictable and exchange-specific
- Market impact is minimal for the position sizes traded

### Sigmoid Weighting Function
Position weights are determined using a sigmoid function:

```python
def sigmoid_function(adjusted_funding, peak_funding_rate):
    abs_funding = abs(adjusted_funding)
    ratio = abs_funding / peak_funding_rate
    y = abs_funding * exp(-(ratio ** 2))
    return y / (peak_funding_rate * exp(-1))  # Normalize
```

**Parameters**:
- **Peak Funding Rate**: 0.3% (point of maximum weight)
- **Shape**: Bell curve with maximum at peak funding rate
- **Range**: [0, 1] normalized weights

## Position Selection (Simplified)

### Selection Process

#### 1. Simple Ranking by Adjusted Funding Rate
```python
# Sort by adjusted annualized funding rate
potential_longs.sort(key=lambda x: x['adjusted_funding'])  # Most negative first
potential_shorts.sort(key=lambda x: x['adjusted_funding'], reverse=True)  # Most positive first
```

#### 2. Top 5 Selection
- **Long Positions**: Top 5 most negative adjusted annualized funding rates
- **Short Positions**: Top 5 most positive adjusted annualized funding rates

#### 3. Long/Short Classification
- **Long Positions**: Negative adjusted funding rates (receive funding)
- **Short Positions**: Positive adjusted funding rates (pay funding)

### Selection Constraints (Simplified)
- **Fixed Positions**: Exactly 5 positions per leg
- **No EV Threshold**: Select top 5 regardless of magnitude
- **Removed**: Complex balancing and conversion logic

## Position Sizing

### Linear Decay Weighting + Volatility Targeting
The strategy uses linear decay weights based on ranking, then applies volatility targeting:

#### 1. Linear Decay Weighting
```python
# Predefined linear decay weights for 5 positions per leg
long_weights = [0.15, 0.125, 0.1, 0.075, 0.05]    # Sums to 0.5
short_weights = [-0.15, -0.125, -0.1, -0.075, -0.05]  # Sums to -0.5

# Apply weights based on ranking (top-ranked gets highest weight)
for i, position in enumerate(long_candidates):
    position['linear_weight'] = long_weights[i]

for i, position in enumerate(short_candidates):
    position['linear_weight'] = short_weights[i]
```

#### 2. Volatility Targeting
```python
target_volatility = 0.20  # 20% annualized

# Apply volatility adjustment to linear weights
for position in candidates:
    leverage = target_volatility / position['weighted_volatility']
    position['vol_adjusted_weight'] = abs(position['linear_weight']) * leverage
    position['size_usd'] = position['vol_adjusted_weight'] * total_capital
```

#### 3. Contract Compliance and Price Validation
```python
# Round position sizes to exchange-specific lot sizes
size_native, actual_position_usd = contract_spec_manager.calculate_position_size_with_rounding(
    symbol, position_size_usd, price)

# Validate contract specifications
is_valid, error_msg = contract_spec_manager.validate_order_specs(
    symbol, size_native, rounded_price)
```

### Position Constraints
- **No Maximum Position Size**: Positions sized based on linear decay weights and volatility targeting
- **Contract Compliance**: All positions rounded to exchange-specific lot sizes and minimum order sizes
- **Price Validation**: All orders validated against exchange tick sizes and specifications

## Target Portfolio Generation

### Portfolio Construction Process

#### 1. Weight Normalization
```python
# Normalize weights to sum to 1.0
total_weight = sum(position['weight'] for position in all_positions)
for position in all_positions:
    position['normalized_weight'] = position['weight'] / total_weight
```

#### 2. Capital Allocation
```python
# Allocate capital based on normalized weights
total_capital = config['total_capital_usd']
for position in all_positions:
    position['size_usd'] = position['normalized_weight'] * total_capital
    position['size_native'] = position['size_usd'] / position['price']
```

#### 3. StrategyPosition Creation
```python
strategy_positions = []
for pos in target_positions:
    strategy_position = StrategyPosition(
        symbol=pos['symbol'],
        side=pos['side'],
        size_usd=pos['size_usd'],
        size_native=pos['size_native'],
        weight=pos['weight'],
        confidence=1.0,
        metadata={
            'adjusted_funding': pos['adjusted_funding'],
            'volatility': pos['volatility'],
            'leverage': pos['leverage'],
            'strategy_source': 'stat_arb_carry_trade'
        }
    )
    strategy_positions.append(strategy_position)
```

### Portfolio Characteristics
- **Market Neutrality**: Balanced long/short exposure
- **Diversification**: Multiple positions across different assets
- **Risk Control**: Volatility-targeted sizing with leverage limits
- **Attribution**: Clear tracking of strategy contribution

## Configuration Parameters

### Key Parameters
```yaml
# Universe Selection
min_daily_volume_usd: 1000000
min_volatility_threshold: 0.05
exclude_new_listings_days: 90      # Increased from 60 to 90 days
min_historical_data_days: 90       # Increased from 60 to 90 days

# Feature Calculation
funding_rate_lookback_days: 3
peak_abs_funding_rate: 0.003

# Position Selection
min_positions_per_leg: 2
max_position_capital_pct: 25

# Position Sizing
target_volatility: 0.30
use_linear_decay_weighting: true
long_weights: [0.15, 0.125, 0.1, 0.075, 0.05]
short_weights: [-0.15, -0.125, -0.1, -0.075, -0.05]
enable_beta_projection: false
beta_neutrality_tolerance: 0.05

# Risk Management
buffer_zone_tolerance_percentage: 5.0
min_close_threshold_usd: 10
```

## Risk Management

### Portfolio-Level Controls
- **EV Threshold**: Close all positions when portfolio EV becomes negative
- **Individual Volatility Targeting**: Each position targeted to 20% volatility
- **Linear Decay Allocation**: Balanced portfolio through predefined weight distribution
- **No Leg Balancing**: Individual volatility targeting eliminates need for leg balancing

### Position-Level Controls
- **Buffer Zones**: 5% tolerance around target positions to prevent over-trading
- **Contract Compliance**: Minimum order sizes and lot size rounding per exchange
- **Price Validation**: Tick size compliance and spread reasonableness checks
- **Position Validation**: Comprehensive validation of all position calculations

### Error Handling
- **Data Validation**: Comprehensive checks on funding rate and price data
- **Graceful Degradation**: Continue operation with partial data
- **Recovery Logic**: Automatic retry and fallback mechanisms
- **Position Monitoring**: Continuous monitoring of active positions

## Performance Characteristics

### Expected Returns
- **Source**: Funding rate capture and mean reversion
- **Frequency**: Daily funding payments at 00:00, 08:00, 16:00 UTC
- **Volatility**: Targeted at 20% annualized
- **Sharpe Ratio**: Target >1.0 through risk management

### Risk Profile
- **Market Risk**: Minimized through balanced long/short allocation (±0.5 weights)
- **Funding Risk**: Primary risk from funding rate changes
- **Liquidity Risk**: Mitigated through volume filters
- **Operational Risk**: Managed through robust error handling and validation

### Performance Metrics
- **Success Rate**: Percentage of profitable rebalancing periods
- **Capital Efficiency**: Return per unit of capital deployed
- **Risk-Adjusted Returns**: Sharpe ratio and maximum drawdown
- **Attribution**: Strategy contribution to overall portfolio
