"""
Simple Unified Database for Trading System

A single, efficient SQLite database that stores all trading system data:
- Market data (OHLCV, funding rates)
- Strategy metrics and performance
- Portfolio metrics
- System state and configuration
- Position tracking

Designed for simplicity, efficiency, and Grafana integration.
"""

import sqlite3
import json
import logging
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import threading

logger = logging.getLogger(__name__)


class TradingDatabase:
    """Simple unified database for all trading system data"""
    
    def __init__(self, db_path: str = "data/trading_system.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._local = threading.local()
        
        # Initialize database
        self._initialize_database()
        logger.info(f"📊 Trading database initialized: {self.db_path}")
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get thread-local database connection"""
        if not hasattr(self._local, 'connection'):
            conn = sqlite3.connect(str(self.db_path), check_same_thread=False)
            
            # Enable WAL mode for better performance
            conn.execute("PRAGMA journal_mode = WAL")
            conn.execute("PRAGMA synchronous = NORMAL")
            conn.execute("PRAGMA cache_size = -64000")  # 64MB cache
            conn.execute("PRAGMA foreign_keys = ON")
            
            # Row factory for dict-like access
            conn.row_factory = sqlite3.Row
            self._local.connection = conn
        
        return self._local.connection
    
    def _initialize_database(self):
        """Initialize database schema"""
        conn = self._get_connection()
        
        # Strategy metrics table (for Grafana)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS strategy_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_name TEXT NOT NULL,
                exchange_name TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                period_start DATETIME,
                period_end DATETIME,
                
                -- Core performance metrics
                total_return REAL,
                annualized_return REAL,
                volatility REAL,
                sharpe_ratio REAL,
                calmar_ratio REAL,
                
                -- Risk metrics
                max_drawdown REAL,
                max_drawdown_duration_days INTEGER,
                
                -- Attribution metrics
                long_return REAL,
                short_return REAL,
                
                -- Trading metrics
                turnover_rate REAL,
                capacity_utilization REAL,
                avg_slippage_bps REAL,
                
                -- Signal metrics
                signal_strength_avg REAL,
                
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Portfolio metrics table (for Grafana)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS portfolio_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME NOT NULL,
                period_start DATETIME,
                period_end DATETIME,
                
                -- Core performance metrics
                total_return REAL,
                annualized_return REAL,
                volatility REAL,
                sharpe_ratio REAL,
                calmar_ratio REAL,
                
                -- Risk metrics
                max_drawdown REAL,
                max_drawdown_duration_days INTEGER,
                
                -- Attribution metrics
                long_return REAL,
                short_return REAL,
                
                -- Trading metrics
                turnover_rate REAL,
                capacity_utilization REAL,
                avg_slippage_bps REAL,
                
                -- Portfolio specific
                total_strategies INTEGER,
                total_positions INTEGER,
                long_positions INTEGER,
                short_positions INTEGER,
                total_capital REAL,
                long_capital REAL,
                short_capital REAL,
                
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Market data table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exchange_name TEXT NOT NULL,
                symbol TEXT NOT NULL,
                data_type TEXT NOT NULL,  -- 'ohlcv', 'funding_rate', 'ticker'
                timeframe TEXT,           -- '1d', '1h', etc.
                timestamp DATETIME NOT NULL,
                data TEXT NOT NULL,       -- JSON data
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                
                UNIQUE(exchange_name, symbol, data_type, timeframe, timestamp)
            )
        """)
        
        # Current positions table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_name TEXT NOT NULL,
                exchange_name TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,       -- 'long' or 'short'
                size_native REAL NOT NULL,
                size_usd REAL NOT NULL,
                entry_price REAL,
                current_price REAL,
                unrealized_pnl REAL,
                weight REAL,
                confidence REAL,
                metadata TEXT,            -- JSON metadata
                
                opened_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                
                UNIQUE(strategy_name, exchange_name, symbol)
            )
        """)
        
        # System state table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS system_state (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,      -- JSON value
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create indexes for performance
        conn.execute("CREATE INDEX IF NOT EXISTS idx_strategy_metrics_time ON strategy_metrics(timestamp)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_portfolio_metrics_time ON portfolio_metrics(timestamp)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON market_data(symbol, timestamp)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_positions_strategy ON positions(strategy_name, exchange_name)")
        
        conn.commit()
    
    # Strategy Metrics Methods
    
    def store_strategy_metrics(self, strategy_name: str, exchange_name: str, metrics: Dict[str, Any]) -> bool:
        """Store strategy performance metrics"""
        try:
            conn = self._get_connection()
            conn.execute("""
                INSERT INTO strategy_metrics (
                    strategy_name, exchange_name, timestamp, period_start, period_end,
                    total_return, annualized_return, volatility, sharpe_ratio, calmar_ratio,
                    max_drawdown, max_drawdown_duration_days, long_return, short_return,
                    turnover_rate, capacity_utilization, avg_slippage_bps, signal_strength_avg
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                strategy_name, exchange_name, metrics.get('timestamp', datetime.now()),
                metrics.get('period_start'), metrics.get('period_end'),
                metrics.get('total_return'), metrics.get('annualized_return'),
                metrics.get('volatility'), metrics.get('sharpe_ratio'), metrics.get('calmar_ratio'),
                metrics.get('max_drawdown'), metrics.get('max_drawdown_duration_days'),
                metrics.get('long_return'), metrics.get('short_return'),
                metrics.get('turnover_rate'), metrics.get('capacity_utilization'),
                metrics.get('avg_slippage_bps'), metrics.get('signal_strength_avg')
            ))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f"❌ Failed to store strategy metrics: {e}")
            return False
    
    def store_portfolio_metrics(self, metrics: Dict[str, Any]) -> bool:
        """Store portfolio performance metrics"""
        try:
            conn = self._get_connection()
            conn.execute("""
                INSERT INTO portfolio_metrics (
                    timestamp, period_start, period_end, total_return, annualized_return,
                    volatility, sharpe_ratio, calmar_ratio, max_drawdown, max_drawdown_duration_days,
                    long_return, short_return, turnover_rate, capacity_utilization, avg_slippage_bps,
                    total_strategies, total_positions, long_positions, short_positions,
                    total_capital, long_capital, short_capital
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                metrics.get('timestamp', datetime.now()), metrics.get('period_start'), metrics.get('period_end'),
                metrics.get('total_return'), metrics.get('annualized_return'), metrics.get('volatility'),
                metrics.get('sharpe_ratio'), metrics.get('calmar_ratio'), metrics.get('max_drawdown'),
                metrics.get('max_drawdown_duration_days'), metrics.get('long_return'), metrics.get('short_return'),
                metrics.get('turnover_rate'), metrics.get('capacity_utilization'), metrics.get('avg_slippage_bps'),
                metrics.get('total_strategies'), metrics.get('total_positions'), metrics.get('long_positions'),
                metrics.get('short_positions'), metrics.get('total_capital'), metrics.get('long_capital'),
                metrics.get('short_capital')
            ))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f"❌ Failed to store portfolio metrics: {e}")
            return False
    
    # Market Data Methods
    
    def store_market_data(self, exchange_name: str, symbol: str, data_type: str, 
                         data: Any, timeframe: str = None) -> bool:
        """Store market data"""
        try:
            conn = self._get_connection()
            conn.execute("""
                INSERT OR REPLACE INTO market_data 
                (exchange_name, symbol, data_type, timeframe, timestamp, data)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                exchange_name, symbol, data_type, timeframe,
                datetime.now(timezone.utc), json.dumps(data)
            ))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f"❌ Failed to store market data: {e}")
            return False
    
    def get_market_data(self, exchange_name: str, symbol: str, data_type: str, 
                       timeframe: str = None, limit: int = 100) -> List[Dict]:
        """Get market data"""
        try:
            conn = self._get_connection()
            query = """
                SELECT data, timestamp FROM market_data
                WHERE exchange_name = ? AND symbol = ? AND data_type = ?
            """
            params = [exchange_name, symbol, data_type]
            
            if timeframe:
                query += " AND timeframe = ?"
                params.append(timeframe)
            
            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)
            
            cursor = conn.execute(query, params)
            results = []
            for row in cursor.fetchall():
                data = json.loads(row['data'])
                data['timestamp'] = row['timestamp']
                results.append(data)
            return results
        except Exception as e:
            logger.error(f"❌ Failed to get market data: {e}")
            return []
    
    # Position Methods
    
    def store_position(self, strategy_name: str, exchange_name: str, position: Dict[str, Any]) -> bool:
        """Store or update position"""
        try:
            conn = self._get_connection()
            conn.execute("""
                INSERT OR REPLACE INTO positions (
                    strategy_name, exchange_name, symbol, side, size_native, size_usd,
                    entry_price, current_price, unrealized_pnl, weight, confidence, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                strategy_name, exchange_name, position.get('symbol'), position.get('side'),
                position.get('size_native'), position.get('size_usd'), position.get('entry_price'),
                position.get('current_price'), position.get('unrealized_pnl'), position.get('weight'),
                position.get('confidence'), json.dumps(position.get('metadata', {}))
            ))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f"❌ Failed to store position: {e}")
            return False
    
    def get_positions(self, strategy_name: str = None, exchange_name: str = None) -> List[Dict]:
        """Get current positions"""
        try:
            conn = self._get_connection()
            query = "SELECT * FROM positions WHERE 1=1"
            params = []
            
            if strategy_name:
                query += " AND strategy_name = ?"
                params.append(strategy_name)
            
            if exchange_name:
                query += " AND exchange_name = ?"
                params.append(exchange_name)
            
            cursor = conn.execute(query, params)
            positions = []
            for row in cursor.fetchall():
                position = dict(row)
                position['metadata'] = json.loads(position['metadata'] or '{}')
                positions.append(position)
            return positions
        except Exception as e:
            logger.error(f"❌ Failed to get positions: {e}")
            return []
    
    # System State Methods
    
    def set_state(self, key: str, value: Any, description: str = None) -> bool:
        """Set system state"""
        try:
            conn = self._get_connection()
            conn.execute("""
                INSERT OR REPLACE INTO system_state (key, value, description, updated_at)
                VALUES (?, ?, ?, ?)
            """, (key, json.dumps(value), description, datetime.now()))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f"❌ Failed to set state: {e}")
            return False
    
    def get_state(self, key: str) -> Any:
        """Get system state"""
        try:
            conn = self._get_connection()
            cursor = conn.execute("SELECT value FROM system_state WHERE key = ?", (key,))
            row = cursor.fetchone()
            return json.loads(row['value']) if row else None
        except Exception as e:
            logger.error(f"❌ Failed to get state: {e}")
            return None
    
    # Utility Methods
    
    def integrity_check(self) -> bool:
        """Check database integrity"""
        try:
            conn = self._get_connection()
            result = conn.execute("PRAGMA integrity_check").fetchone()
            return result[0] == "ok"
        except Exception as e:
            logger.error(f"❌ Integrity check failed: {e}")
            return False
    
    def vacuum(self) -> bool:
        """Vacuum database to reclaim space"""
        try:
            conn = self._get_connection()
            conn.execute("VACUUM")
            logger.info("🧹 Database vacuumed successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Database vacuum failed: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            conn = self._get_connection()
            
            stats = {}
            tables = ['strategy_metrics', 'portfolio_metrics', 'market_data', 'positions', 'system_state']
            
            for table in tables:
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
                stats[f"{table}_count"] = cursor.fetchone()[0]
            
            # Database file size
            stats['db_size_mb'] = self.db_path.stat().st_size / 1024 / 1024
            
            return stats
        except Exception as e:
            logger.error(f"❌ Failed to get stats: {e}")
            return {}


# Global database instance
_global_db: Optional[TradingDatabase] = None


def get_database(db_path: str = "data/trading_system.db") -> TradingDatabase:
    """Get global database instance"""
    global _global_db
    if _global_db is None:
        _global_db = TradingDatabase(db_path)
    return _global_db
