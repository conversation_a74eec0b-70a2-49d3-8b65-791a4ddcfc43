#!/usr/bin/env python3
"""
Test script for unified trading database

Verifies all database functionality works correctly.
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from storage.database import get_database
from data.cache import get_cache, CacheKeys

logger = logging.getLogger(__name__)


def test_database_operations():
    """Test basic database operations"""
    print("🧪 Testing database operations...")
    
    try:
        db = get_database()
        
        # Test strategy metrics
        strategy_metrics = {
            'timestamp': datetime.now(),
            'period_start': datetime.now(),
            'period_end': datetime.now(),
            'total_return': 0.05,
            'annualized_return': 0.12,
            'volatility': 0.15,
            'sharpe_ratio': 1.2,
            'calmar_ratio': 0.8,
            'max_drawdown': -0.08,
            'max_drawdown_duration_days': 15,
            'long_return': 0.03,
            'short_return': 0.02,
            'turnover_rate': 0.25,
            'capacity_utilization': 0.85,
            'avg_slippage_bps': 2.5,
            'signal_strength_avg': 0.7
        }
        
        success = db.store_strategy_metrics("test_strategy", "bybit", strategy_metrics)
        print(f"✅ Strategy metrics stored: {success}")
        
        # Test portfolio metrics
        portfolio_metrics = {
            'timestamp': datetime.now(),
            'period_start': datetime.now(),
            'period_end': datetime.now(),
            'total_return': 0.08,
            'annualized_return': 0.18,
            'volatility': 0.12,
            'sharpe_ratio': 1.5,
            'calmar_ratio': 1.2,
            'max_drawdown': -0.06,
            'max_drawdown_duration_days': 10,
            'long_return': 0.04,
            'short_return': 0.04,
            'turnover_rate': 0.20,
            'capacity_utilization': 0.90,
            'avg_slippage_bps': 2.0,
            'total_strategies': 3,
            'total_positions': 25,
            'long_positions': 15,
            'short_positions': 10,
            'total_capital': 1000000.0,
            'long_capital': 600000.0,
            'short_capital': 400000.0
        }
        
        success = db.store_portfolio_metrics(portfolio_metrics)
        print(f"✅ Portfolio metrics stored: {success}")
        
        # Test market data
        market_data = {
            'open': 50000.0,
            'high': 51000.0,
            'low': 49500.0,
            'close': 50500.0,
            'volume': 1000000.0
        }
        
        success = db.store_market_data("bybit", "BTCUSDT", "ohlcv", market_data, "1d")
        print(f"✅ Market data stored: {success}")
        
        # Test retrieving market data
        retrieved_data = db.get_market_data("bybit", "BTCUSDT", "ohlcv", "1d", limit=5)
        print(f"✅ Market data retrieved: {len(retrieved_data)} records")
        
        # Test position storage
        position = {
            'symbol': 'BTCUSDT',
            'side': 'long',
            'size_native': 0.5,
            'size_usd': 25000.0,
            'entry_price': 50000.0,
            'current_price': 50500.0,
            'unrealized_pnl': 250.0,
            'weight': 0.025,
            'confidence': 0.8,
            'metadata': {'signal_strength': 0.7}
        }
        
        success = db.store_position("test_strategy", "bybit", position)
        print(f"✅ Position stored: {success}")
        
        # Test retrieving positions
        positions = db.get_positions("test_strategy", "bybit")
        print(f"✅ Positions retrieved: {len(positions)} positions")
        
        # Test system state
        success = db.set_state("test_key", {"test": "value"}, "Test state")
        print(f"✅ System state stored: {success}")
        
        state_value = db.get_state("test_key")
        print(f"✅ System state retrieved: {state_value}")
        
        # Test database stats
        stats = db.get_stats()
        print(f"✅ Database stats: {stats}")
        
        # Test integrity check
        integrity_ok = db.integrity_check()
        print(f"✅ Database integrity: {integrity_ok}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


def test_cache_operations():
    """Test cache operations"""
    print("\n🧪 Testing cache operations...")
    
    try:
        cache = get_cache()
        
        # Test basic cache operations
        test_data = {"price": 50000, "volume": 1000}
        key = CacheKeys.market_data("bybit", "BTCUSDT", "ticker")
        
        # Store in cache
        cache.set(key, test_data, ttl=300)
        print("✅ Data stored in cache")
        
        # Retrieve from cache
        cached_data = cache.get(key)
        print(f"✅ Data retrieved from cache: {cached_data}")
        
        # Test cache miss
        missing_key = CacheKeys.market_data("binance", "ETHUSDT", "ticker")
        missing_data = cache.get(missing_key)
        print(f"✅ Cache miss handled: {missing_data is None}")
        
        # Test cache stats
        stats = cache.get_stats()
        print(f"✅ Cache stats: {stats}")
        
        # Test cache cleanup
        expired_count = cache.cleanup_expired()
        print(f"✅ Cache cleanup: {expired_count} expired items removed")
        
        return True
        
    except Exception as e:
        print(f"❌ Cache test failed: {e}")
        return False


def test_integration():
    """Test integration between database and cache"""
    print("\n🧪 Testing integration...")
    
    try:
        db = get_database()
        cache = get_cache()
        
        # Simulate typical workflow
        symbol = "ETHUSDT"
        exchange = "bybit"
        
        # 1. Check cache first
        cache_key = CacheKeys.market_data(exchange, symbol, "ticker")
        cached_ticker = cache.get(cache_key)
        
        if cached_ticker is None:
            # 2. Fetch from database (simulated)
            ticker_data = {"price": 3000, "volume": 500000, "timestamp": datetime.now()}
            
            # 3. Store in database
            db.store_market_data(exchange, symbol, "ticker", ticker_data)
            
            # 4. Cache the result
            cache.set(cache_key, ticker_data, ttl=60)
            
            print("✅ Data fetched and cached")
        else:
            print("✅ Data served from cache")
        
        # Test that subsequent requests use cache
        cached_ticker = cache.get(cache_key)
        print(f"✅ Cache hit: {cached_ticker is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 Testing unified trading database system")
    print("=" * 50)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    tests = [
        ("Database operations", test_database_operations),
        ("Cache operations", test_cache_operations),
        ("Integration", test_integration)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n📦 Running {test_name} test...")
        if not test_func():
            print(f"❌ {test_name} test failed!")
            all_passed = False
        else:
            print(f"✅ {test_name} test passed!")
    
    if all_passed:
        print("\n🎉 All tests passed!")
        print("\nThe unified storage system is working correctly.")
    else:
        print("\n❌ Some tests failed!")
        print("Please check the error messages above.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
