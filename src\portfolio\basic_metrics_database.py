"""
Basic database storage for essential portfolio metrics

Simple adapter for the unified trading database that maintains compatibility
with existing Grafana dashboards while using the new unified storage system.
"""

import logging
from typing import Dict, List, Optional, Any
from storage.database import get_database
from .basic_metrics_calculator import BasicPerformanceMetrics

logger = logging.getLogger(__name__)


class BasicMetricsDatabase:
    """Simple adapter for unified database that maintains Grafana compatibility"""
    
    def __init__(self, db_path: str = "data/trading_system.db"):
        """Initialize with unified database"""
        self.db = get_database(db_path)
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"📊 Basic metrics database adapter initialized")
    
    def store_basic_strategy_metrics(self, strategy_name: str, 
                                   metrics: BasicPerformanceMetrics) -> bool:
        """Store basic strategy performance metrics"""
        try:
            metrics_dict = {
                'timestamp': metrics.calculation_timestamp,
                'period_start': metrics.period_start,
                'period_end': metrics.period_end,
                'total_return': metrics.total_return,
                'annualized_return': metrics.annualized_return,
                'volatility': metrics.volatility,
                'sharpe_ratio': metrics.sharpe_ratio,
                'calmar_ratio': metrics.calmar_ratio,
                'max_drawdown': metrics.max_drawdown,
                'max_drawdown_duration_days': metrics.max_drawdown_duration_days,
                'long_return': metrics.long_return,
                'short_return': metrics.short_return,
                'turnover_rate': metrics.turnover_rate,
                'capacity_utilization': metrics.capacity_utilization,
                'avg_slippage_bps': metrics.avg_slippage_bps,
                'signal_strength_avg': metrics.signal_strength_avg
            }
            
            return self.db.store_strategy_metrics(strategy_name, "bybit", metrics_dict)
            
        except Exception as e:
            self.logger.error(f"Failed to store basic strategy metrics for {strategy_name}: {e}")
            return False

    def store_basic_portfolio_metrics(self, metrics: BasicPerformanceMetrics,
                                    portfolio_stats: Dict[str, Any]) -> bool:
        """Store basic aggregate portfolio metrics"""
        try:
            metrics_dict = {
                'timestamp': metrics.calculation_timestamp,
                'period_start': metrics.period_start,
                'period_end': metrics.period_end,
                'total_return': metrics.total_return,
                'annualized_return': metrics.annualized_return,
                'volatility': metrics.volatility,
                'sharpe_ratio': metrics.sharpe_ratio,
                'calmar_ratio': metrics.calmar_ratio,
                'max_drawdown': metrics.max_drawdown,
                'max_drawdown_duration_days': metrics.max_drawdown_duration_days,
                'long_return': metrics.long_return,
                'short_return': metrics.short_return,
                'turnover_rate': metrics.turnover_rate,
                'capacity_utilization': metrics.capacity_utilization,
                'avg_slippage_bps': metrics.avg_slippage_bps,
                'total_strategies': portfolio_stats.get('total_strategies', 0),
                'total_positions': portfolio_stats.get('total_positions', 0),
                'long_positions': portfolio_stats.get('long_positions', 0),
                'short_positions': portfolio_stats.get('short_positions', 0),
                'total_capital': portfolio_stats.get('total_capital', 0.0),
                'long_capital': portfolio_stats.get('long_capital', 0.0),
                'short_capital': portfolio_stats.get('short_capital', 0.0)
            }
            
            return self.db.store_portfolio_metrics(metrics_dict)
            
        except Exception as e:
            self.logger.error(f"Failed to store basic portfolio metrics: {e}")
            return False
    
    def get_basic_strategy_metrics(self, strategy_name: str = None, 
                                 limit: int = 100) -> List[Dict[str, Any]]:
        """Get basic strategy metrics for Grafana"""
        try:
            # This would need to be implemented in the unified database
            # For now, return empty list
            return []
        except Exception as e:
            self.logger.error(f"Failed to get basic strategy metrics: {e}")
            return []
    
    def get_basic_portfolio_metrics(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get basic portfolio metrics for Grafana"""
        try:
            # This would need to be implemented in the unified database
            # For now, return empty list
            return []
        except Exception as e:
            self.logger.error(f"Failed to get basic portfolio metrics: {e}")
            return []
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            return self.db.get_stats()
        except Exception as e:
            self.logger.error(f"Failed to get database stats: {e}")
            return {}
    
    def check_integrity(self) -> bool:
        """Check database integrity"""
        try:
            return self.db.integrity_check()
        except Exception as e:
            self.logger.error(f"Failed to check database integrity: {e}")
            return False
    
    def vacuum_database(self) -> bool:
        """Vacuum database to reclaim space"""
        try:
            return self.db.vacuum()
        except Exception as e:
            self.logger.error(f"Failed to vacuum database: {e}")
            return False
