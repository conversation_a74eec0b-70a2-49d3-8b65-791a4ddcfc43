"""
Custom assertion helpers for testing trading system components
"""

import math
from typing import Dict, List, Any, Optional
from decimal import <PERSON><PERSON><PERSON>


def assert_position_equal(
    actual: Dict[str, Any],
    expected: Dict[str, Any],
    tolerance: float = 1e-6,
    msg: Optional[str] = None
):
    """
    Assert that two positions are equal within tolerance
    
    Args:
        actual: Actual position dictionary
        expected: Expected position dictionary
        tolerance: Numerical tolerance for comparisons
        msg: Optional error message
    """
    if msg is None:
        msg = f"Positions not equal: {actual} != {expected}"
    
    # Check symbol
    assert actual.get("symbol") == expected.get("symbol"), f"{msg} - Symbol mismatch"
    
    # Check side
    assert actual.get("side") == expected.get("side"), f"{msg} - Side mismatch"
    
    # Check size with tolerance
    actual_size = float(actual.get("size", 0))
    expected_size = float(expected.get("size", 0))
    assert abs(actual_size - expected_size) <= tolerance, f"{msg} - Size mismatch: {actual_size} != {expected_size}"
    
    # Check price with tolerance if present
    if "price" in actual and "price" in expected:
        actual_price = float(actual["price"])
        expected_price = float(expected["price"])
        assert abs(actual_price - expected_price) <= tolerance, f"{msg} - Price mismatch"


def assert_portfolio_equal(
    actual: List[Dict[str, Any]],
    expected: List[Dict[str, Any]],
    tolerance: float = 1e-6,
    msg: Optional[str] = None
):
    """
    Assert that two portfolios are equal within tolerance
    
    Args:
        actual: Actual portfolio (list of positions)
        expected: Expected portfolio (list of positions)
        tolerance: Numerical tolerance for comparisons
        msg: Optional error message
    """
    if msg is None:
        msg = "Portfolios not equal"
    
    assert len(actual) == len(expected), f"{msg} - Portfolio size mismatch: {len(actual)} != {len(expected)}"
    
    # Sort both portfolios by symbol for comparison
    actual_sorted = sorted(actual, key=lambda x: x.get("symbol", ""))
    expected_sorted = sorted(expected, key=lambda x: x.get("symbol", ""))
    
    for i, (actual_pos, expected_pos) in enumerate(zip(actual_sorted, expected_sorted)):
        assert_position_equal(actual_pos, expected_pos, tolerance, f"{msg} - Position {i}")


def assert_metrics_valid(
    metrics: Dict[str, Any],
    required_fields: Optional[List[str]] = None,
    msg: Optional[str] = None
):
    """
    Assert that performance metrics are valid
    
    Args:
        metrics: Metrics dictionary to validate
        required_fields: List of required field names
        msg: Optional error message
    """
    if msg is None:
        msg = "Invalid metrics"
    
    if required_fields is None:
        required_fields = [
            "total_return", "annualized_return", "sharpe_ratio",
            "max_drawdown", "volatility"
        ]
    
    # Check required fields exist
    for field in required_fields:
        assert field in metrics, f"{msg} - Missing required field: {field}"
    
    # Validate numerical ranges
    if "sharpe_ratio" in metrics:
        sharpe = metrics["sharpe_ratio"]
        if sharpe is not None:
            assert -10 <= sharpe <= 10, f"{msg} - Invalid Sharpe ratio: {sharpe}"
    
    if "max_drawdown" in metrics:
        drawdown = metrics["max_drawdown"]
        if drawdown is not None:
            assert 0 <= drawdown <= 1, f"{msg} - Invalid max drawdown: {drawdown}"
    
    if "volatility" in metrics:
        vol = metrics["volatility"]
        if vol is not None:
            assert 0 <= vol <= 5, f"{msg} - Invalid volatility: {vol}"


def assert_calculation_accuracy(
    calculated: float,
    expected: float,
    tolerance: float = 1e-6,
    relative_tolerance: float = 1e-4,
    msg: Optional[str] = None
):
    """
    Assert calculation accuracy with both absolute and relative tolerance
    
    Args:
        calculated: Calculated value
        expected: Expected value
        tolerance: Absolute tolerance
        relative_tolerance: Relative tolerance (percentage)
        msg: Optional error message
    """
    if msg is None:
        msg = f"Calculation inaccurate: {calculated} != {expected}"
    
    # Check for NaN or infinite values
    assert not math.isnan(calculated), f"{msg} - Calculated value is NaN"
    assert not math.isinf(calculated), f"{msg} - Calculated value is infinite"
    assert not math.isnan(expected), f"{msg} - Expected value is NaN"
    assert not math.isinf(expected), f"{msg} - Expected value is infinite"
    
    # Absolute tolerance check
    abs_diff = abs(calculated - expected)
    assert abs_diff <= tolerance, f"{msg} - Absolute difference {abs_diff} > tolerance {tolerance}"
    
    # Relative tolerance check (if expected is not zero)
    if abs(expected) > tolerance:
        rel_diff = abs_diff / abs(expected)
        assert rel_diff <= relative_tolerance, f"{msg} - Relative difference {rel_diff} > tolerance {relative_tolerance}"


def assert_units_consistent(
    value: float,
    expected_unit: str,
    context: str = "",
    msg: Optional[str] = None
):
    """
    Assert that a value is consistent with expected units
    
    Args:
        value: Numerical value to check
        expected_unit: Expected unit type ("percentage", "usd", "rate", "ratio")
        context: Context for the assertion
        msg: Optional error message
    """
    if msg is None:
        msg = f"Unit inconsistency in {context}: {value} not consistent with {expected_unit}"
    
    if expected_unit == "percentage":
        # Percentages should typically be between -100% and 1000%
        assert -1.0 <= value <= 10.0, f"{msg} - Percentage out of reasonable range"
    
    elif expected_unit == "rate":
        # Rates (like funding rates) should be reasonable
        # Annualized rates can be larger than periodic rates
        if "annualized" in context.lower():
            assert -1.0 <= value <= 1.0, f"{msg} - Annualized rate out of reasonable range"
        else:
            assert -0.1 <= value <= 0.1, f"{msg} - Rate out of reasonable range"
    
    elif expected_unit == "usd":
        # USD values should be positive for sizes, can be negative for PnL
        if "size" in context.lower() or "amount" in context.lower():
            assert value >= 0, f"{msg} - USD size should be non-negative"
    
    elif expected_unit == "ratio":
        # Ratios should be reasonable
        assert -100 <= value <= 100, f"{msg} - Ratio out of reasonable range"


def assert_funding_rate_calculation(
    rate_8h: float,
    annualized_rate: float,
    exchange: str = "bybit",
    tolerance: float = 1e-6
):
    """
    Assert funding rate annualization is correct
    
    Args:
        rate_8h: 8-hour funding rate
        annualized_rate: Annualized funding rate
        exchange: Exchange name for rate frequency
        tolerance: Numerical tolerance
    """
    # Different exchanges have different funding frequencies
    funding_frequencies = {
        "bybit": 3,      # 3 times per day (8h)
        "binance": 3,    # 3 times per day (8h)
        "okx": 3,        # 3 times per day (8h)
        "hyperliquid": 24  # 24 times per day (1h)
    }
    
    frequency = funding_frequencies.get(exchange.lower(), 3)
    expected_annualized = rate_8h * frequency * 365
    
    assert_calculation_accuracy(
        annualized_rate,
        expected_annualized,
        tolerance,
        msg=f"Funding rate annualization incorrect for {exchange}"
    )


def assert_volatility_calculation(
    returns: List[float],
    calculated_vol: float,
    annualization_factor: float = 365,
    tolerance: float = 1e-6
):
    """
    Assert volatility calculation is correct
    
    Args:
        returns: List of returns
        calculated_vol: Calculated volatility
        annualization_factor: Annualization factor (365 for daily returns)
        tolerance: Numerical tolerance
    """
    import numpy as np
    
    # Calculate expected volatility
    returns_array = np.array(returns)
    expected_vol = np.std(returns_array, ddof=1) * math.sqrt(annualization_factor)
    
    assert_calculation_accuracy(
        calculated_vol,
        expected_vol,
        tolerance,
        msg="Volatility calculation incorrect"
    )


def assert_position_sizing_valid(
    positions: List[Dict[str, Any]],
    total_capital: float,
    max_leverage: float = 10.0,
    tolerance: float = 1e-6
):
    """
    Assert that position sizing is valid and within risk limits
    
    Args:
        positions: List of positions
        total_capital: Total available capital
        max_leverage: Maximum allowed leverage
        tolerance: Numerical tolerance
    """
    total_notional = 0.0
    
    for position in positions:
        size = float(position.get("size", 0))
        price = float(position.get("price", 0))
        notional = abs(size * price)
        total_notional += notional
        
        # Check individual position is reasonable
        assert notional >= 0, f"Position notional should be non-negative: {notional}"
        assert notional <= total_capital * max_leverage, f"Position too large: {notional} > {total_capital * max_leverage}"
    
    # Check total leverage
    total_leverage = total_notional / total_capital if total_capital > 0 else 0
    assert total_leverage <= max_leverage + tolerance, f"Total leverage too high: {total_leverage} > {max_leverage}"


def assert_portfolio_weights_sum_to_one(
    weights: Dict[str, float],
    tolerance: float = 1e-6
):
    """
    Assert that portfolio weights sum to 1.0
    
    Args:
        weights: Dictionary of symbol -> weight
        tolerance: Numerical tolerance
    """
    total_weight = sum(abs(weight) for weight in weights.values())
    assert abs(total_weight - 1.0) <= tolerance, f"Portfolio weights don't sum to 1.0: {total_weight}"


def assert_no_duplicate_symbols(
    positions: List[Dict[str, Any]]
):
    """
    Assert that there are no duplicate symbols in positions
    
    Args:
        positions: List of positions
    """
    symbols = [pos.get("symbol") for pos in positions]
    unique_symbols = set(symbols)
    assert len(symbols) == len(unique_symbols), f"Duplicate symbols found: {symbols}"


def assert_data_freshness(
    timestamp: int,
    max_age_seconds: int = 3600,
    msg: Optional[str] = None
):
    """
    Assert that data is fresh (not too old)
    
    Args:
        timestamp: Data timestamp in milliseconds
        max_age_seconds: Maximum allowed age in seconds
        msg: Optional error message
    """
    import time
    
    if msg is None:
        msg = "Data is too old"
    
    current_time = int(time.time() * 1000)
    age_ms = current_time - timestamp
    age_seconds = age_ms / 1000
    
    assert age_seconds <= max_age_seconds, f"{msg} - Data age: {age_seconds}s > {max_age_seconds}s"
