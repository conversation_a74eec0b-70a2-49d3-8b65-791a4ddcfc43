"""
Comprehensive test runner for the trading system

This script runs all tests in the proper order:
1. Unit tests
2. Integration tests  
3. Validation tests
4. Real API tests (optional)
5. End-to-end tests
"""

import sys
import os
import asyncio
import argparse
from pathlib import Path
import subprocess
import time
from typing import Dict, List, Tuple

# Add src to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

# Test categories and their directories
TEST_CATEGORIES = {
    'unit': 'tests/unit',
    'integration': 'tests/integration', 
    'validation': 'tests/validation',
    'real_api': 'tests/real_api',
    'end_to_end': 'tests/end_to_end'
}

# Test execution order (some tests depend on others)
TEST_ORDER = ['unit', 'validation', 'integration', 'real_api', 'end_to_end']


class TestRunner:
    """Comprehensive test runner"""
    
    def __init__(self, verbose: bool = False, fail_fast: bool = False):
        self.verbose = verbose
        self.fail_fast = fail_fast
        self.results = {}
        self.start_time = None
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.skipped_tests = 0
    
    def run_pytest_category(self, category: str, test_dir: str) -> Tuple[bool, Dict]:
        """Run pytest for a specific test category"""
        print(f"\n{'='*60}")
        print(f"Running {category.upper()} tests from {test_dir}")
        print(f"{'='*60}")
        
        if not Path(test_dir).exists():
            print(f"⚠️  Test directory {test_dir} does not exist, skipping...")
            return True, {'passed': 0, 'failed': 0, 'skipped': 0, 'duration': 0}
        
        # Build pytest command
        cmd = ['python', '-m', 'pytest', test_dir]
        
        if self.verbose:
            cmd.extend(['-v', '-s'])
        else:
            cmd.append('-q')
        
        if self.fail_fast:
            cmd.append('-x')
        
        # Add coverage for unit tests
        if category == 'unit':
            cmd.extend(['--cov=src', '--cov-report=term-missing'])
        
        # Add markers for different test types
        if category == 'real_api':
            cmd.extend(['-m', 'not slow'])  # Skip slow tests by default
        
        # Run tests
        start_time = time.time()
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout per category
            )
            duration = time.time() - start_time
            
            # Parse pytest output
            output = result.stdout + result.stderr
            stats = self._parse_pytest_output(output)
            stats['duration'] = duration
            
            success = result.returncode == 0
            
            if success:
                print(f"✅ {category.upper()} tests passed")
            else:
                print(f"❌ {category.upper()} tests failed")
                if self.verbose:
                    print(f"STDOUT:\n{result.stdout}")
                    print(f"STDERR:\n{result.stderr}")
            
            return success, stats
            
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            print(f"⏰ {category.upper()} tests timed out after {duration:.1f}s")
            return False, {'passed': 0, 'failed': 1, 'skipped': 0, 'duration': duration}
        
        except Exception as e:
            duration = time.time() - start_time
            print(f"💥 Error running {category.upper()} tests: {e}")
            return False, {'passed': 0, 'failed': 1, 'skipped': 0, 'duration': duration}
    
    def _parse_pytest_output(self, output: str) -> Dict:
        """Parse pytest output to extract test statistics"""
        stats = {'passed': 0, 'failed': 0, 'skipped': 0, 'errors': 0}
        
        # Look for pytest summary line
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            
            # Look for summary line like "5 passed, 2 failed, 1 skipped"
            if 'passed' in line or 'failed' in line or 'skipped' in line:
                parts = line.split(',')
                for part in parts:
                    part = part.strip()
                    if 'passed' in part:
                        try:
                            stats['passed'] = int(part.split()[0])
                        except:
                            pass
                    elif 'failed' in part:
                        try:
                            stats['failed'] = int(part.split()[0])
                        except:
                            pass
                    elif 'skipped' in part:
                        try:
                            stats['skipped'] = int(part.split()[0])
                        except:
                            pass
                    elif 'error' in part:
                        try:
                            stats['errors'] = int(part.split()[0])
                        except:
                            pass
        
        return stats
    
    async def run_real_api_tests(self) -> Tuple[bool, Dict]:
        """Run real API tests with proper async handling"""
        print(f"\n{'='*60}")
        print("Running REAL API tests")
        print(f"{'='*60}")
        
        try:
            from tests.helpers.real_api_helper import run_real_api_tests
            
            # Test each exchange
            exchanges = ['bybit']  # Start with bybit only
            all_passed = True
            total_stats = {'passed': 0, 'failed': 0, 'skipped': 0, 'duration': 0}
            
            for exchange in exchanges:
                print(f"\n🔌 Testing {exchange.upper()} API...")
                start_time = time.time()
                
                try:
                    success = await run_real_api_tests(exchange)
                    duration = time.time() - start_time
                    
                    if success:
                        print(f"✅ {exchange.upper()} API tests passed")
                        total_stats['passed'] += 1
                    else:
                        print(f"❌ {exchange.upper()} API tests failed")
                        total_stats['failed'] += 1
                        all_passed = False
                    
                    total_stats['duration'] += duration
                    
                except Exception as e:
                    duration = time.time() - start_time
                    print(f"💥 Error testing {exchange.upper()} API: {e}")
                    total_stats['failed'] += 1
                    total_stats['duration'] += duration
                    all_passed = False
            
            return all_passed, total_stats
            
        except ImportError as e:
            print(f"⚠️  Cannot import real API test helper: {e}")
            return True, {'passed': 0, 'failed': 0, 'skipped': 1, 'duration': 0}
        except Exception as e:
            print(f"💥 Error running real API tests: {e}")
            return False, {'passed': 0, 'failed': 1, 'skipped': 0, 'duration': 0}
    
    def print_summary(self):
        """Print comprehensive test summary"""
        total_duration = time.time() - self.start_time if self.start_time else 0
        
        print(f"\n{'='*80}")
        print("COMPREHENSIVE TEST SUMMARY")
        print(f"{'='*80}")
        
        # Category results
        for category in TEST_ORDER:
            if category in self.results:
                success, stats = self.results[category]
                status = "✅ PASSED" if success else "❌ FAILED"
                print(f"{category.upper():15} {status:10} "
                      f"P:{stats['passed']:3} F:{stats['failed']:3} S:{stats['skipped']:3} "
                      f"({stats['duration']:.1f}s)")
        
        print(f"{'-'*80}")
        
        # Overall statistics
        print(f"Total Duration: {total_duration:.1f}s")
        print(f"Total Tests:    {self.total_tests}")
        print(f"Passed:         {self.passed_tests}")
        print(f"Failed:         {self.failed_tests}")
        print(f"Skipped:        {self.skipped_tests}")
        
        # Overall result
        overall_success = self.failed_tests == 0
        if overall_success:
            print(f"\n🎉 ALL TESTS PASSED! 🎉")
        else:
            print(f"\n💥 {self.failed_tests} TEST(S) FAILED 💥")
        
        print(f"{'='*80}")
        
        return overall_success
    
    async def run_all_tests(self, categories: List[str] = None, skip_real_api: bool = False) -> bool:
        """Run all test categories"""
        self.start_time = time.time()
        
        if categories is None:
            categories = TEST_ORDER.copy()
            if skip_real_api:
                categories.remove('real_api')
        
        print("🚀 Starting comprehensive test suite...")
        print(f"Categories to run: {', '.join(categories)}")
        
        overall_success = True
        
        for category in categories:
            if category == 'real_api':
                # Special handling for real API tests
                success, stats = await self.run_real_api_tests()
            else:
                # Regular pytest categories
                test_dir = TEST_CATEGORIES.get(category)
                if test_dir:
                    success, stats = self.run_pytest_category(category, test_dir)
                else:
                    print(f"⚠️  Unknown test category: {category}")
                    continue
            
            # Record results
            self.results[category] = (success, stats)
            
            # Update totals
            self.total_tests += stats['passed'] + stats['failed'] + stats['skipped']
            self.passed_tests += stats['passed']
            self.failed_tests += stats['failed']
            self.skipped_tests += stats['skipped']
            
            if not success:
                overall_success = False
                if self.fail_fast:
                    print(f"💥 Stopping due to failure in {category} tests (fail-fast mode)")
                    break
        
        return overall_success
    
    def run_specific_tests(self, test_paths: List[str]) -> bool:
        """Run specific test files or directories"""
        print(f"🎯 Running specific tests: {', '.join(test_paths)}")
        
        cmd = ['python', '-m', 'pytest'] + test_paths
        
        if self.verbose:
            cmd.extend(['-v', '-s'])
        
        if self.fail_fast:
            cmd.append('-x')
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Specific tests passed")
                return True
            else:
                print("❌ Specific tests failed")
                if self.verbose:
                    print(f"STDOUT:\n{result.stdout}")
                    print(f"STDERR:\n{result.stderr}")
                return False
                
        except Exception as e:
            print(f"💥 Error running specific tests: {e}")
            return False


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Comprehensive test runner for trading system")
    
    parser.add_argument(
        '--categories', '-c',
        nargs='+',
        choices=list(TEST_CATEGORIES.keys()),
        help='Test categories to run'
    )
    
    parser.add_argument(
        '--skip-real-api',
        action='store_true',
        help='Skip real API tests'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output'
    )
    
    parser.add_argument(
        '--fail-fast', '-x',
        action='store_true',
        help='Stop on first failure'
    )
    
    parser.add_argument(
        '--specific', '-s',
        nargs='+',
        help='Run specific test files or directories'
    )
    
    args = parser.parse_args()
    
    # Create test runner
    runner = TestRunner(verbose=args.verbose, fail_fast=args.fail_fast)
    
    async def run_tests():
        if args.specific:
            # Run specific tests
            success = runner.run_specific_tests(args.specific)
        else:
            # Run test categories
            success = await runner.run_all_tests(
                categories=args.categories,
                skip_real_api=args.skip_real_api
            )
            
            # Print summary
            success = runner.print_summary()
        
        return success
    
    # Run tests
    try:
        success = asyncio.run(run_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
