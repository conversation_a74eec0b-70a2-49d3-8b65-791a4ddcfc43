["end_to_end/full_system/test_system_integration.py::TestFullSystemWorkflow::test_complete_trading_workflow_simulation", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_concurrent_operations", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_config_loading_and_validation", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_configuration_edge_cases", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_data_fetching_pipeline", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_error_handling_throughout_system", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_exchange_factory_and_initialization", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_memory_usage_and_cleanup", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_performance_tracking_pipeline", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_portfolio_combination_pipeline", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_strategy_execution_pipeline", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_system_state_consistency", "integration/data_flow/test_data_pipeline.py::TestDataFlowIntegration::test_basic_data_flow", "integration/data_flow/test_data_pipeline.py::TestDataFlowIntegration::test_concurrent_data_flow", "integration/data_flow/test_data_pipeline.py::TestDataFlowIntegration::test_multiple_symbol_data_flow", "integration/order_execution/test_execution_integration.py::TestOrderExecutionIntegration::test_basic_order_execution", "integration/order_execution/test_execution_integration.py::TestOrderExecutionIntegration::test_market_order_execution", "integration/order_execution/test_execution_integration.py::TestOrderExecutionIntegration::test_order_cancellation", "integration/portfolio_combination/test_portfolio_integration.py::TestPortfolioCombinationIntegration::test_basic_portfolio_combination", "integration/portfolio_combination/test_portfolio_integration.py::TestPortfolioCombinationIntegration::test_position_netting", "integration/strategy_execution/test_strategy_workflow.py::TestStrategyExecutionIntegration::test_basic_strategy_workflow", "integration/strategy_execution/test_strategy_workflow.py::TestStrategyExecutionIntegration::test_strategy_data_requirements", "test_structure_verification.py::TestStructureVerification::test_assertion_helpers_functionality", "test_structure_verification.py::TestStructureVerification::test_can_import_fixtures", "test_structure_verification.py::TestStructureVerification::test_can_import_helpers", "test_structure_verification.py::TestStructureVerification::test_data_generators_functionality", "test_structure_verification.py::TestStructureVerification::test_end_to_end_test_exists", "test_structure_verification.py::TestStructureVerification::test_fixture_files_exist", "test_structure_verification.py::TestStructureVerification::test_helper_files_exist", "test_structure_verification.py::TestStructureVerification::test_mock_exchange_functionality", "test_structure_verification.py::TestStructureVerification::test_pytest_config_exists", "test_structure_verification.py::TestStructureVerification::test_readme_exists", "test_structure_verification.py::TestStructureVerification::test_real_api_test_exists", "test_structure_verification.py::TestStructureVerification::test_sample_unit_test_exists", "test_structure_verification.py::TestStructureVerification::test_test_directories_exist", "test_structure_verification.py::TestStructureVerification::test_test_runner_exists", "test_structure_verification.py::TestStructureVerification::test_unit_test_subdirectories_exist", "test_structure_verification.py::TestStructureVerification::test_validation_test_exists", "test_structure_verification.py::TestTestRunnerFunctionality::test_pytest_markers_are_defined", "test_structure_verification.py::TestTestRunnerFunctionality::test_test_runner_can_be_imported", "unit/config/test_settings.py::TestConfig::test_config_get_with_default", "unit/config/test_settings.py::TestConfig::test_config_initialization_with_dict", "unit/config/test_settings.py::TestConfig::test_config_nested_access", "unit/config/test_settings.py::TestConfig::test_config_set", "unit/config/test_settings.py::TestConfig::test_config_to_dict", "unit/config/test_settings.py::TestConfig::test_config_update", "unit/config/test_settings.py::TestConfigDefaults::test_default_config_structure", "unit/config/test_settings.py::TestConfigDefaults::test_default_exchange_settings", "unit/config/test_settings.py::TestConfigDefaults::test_default_execution_settings", "unit/config/test_settings.py::TestConfigDefaults::test_default_risk_settings", "unit/config/test_settings.py::TestConfigDefaults::test_default_strategy_settings", "unit/config/test_settings.py::TestConfigTypes::test_boolean_types", "unit/config/test_settings.py::TestConfigTypes::test_dictionary_types", "unit/config/test_settings.py::TestConfigTypes::test_numeric_types", "unit/config/test_settings.py::TestConfigTypes::test_string_types", "unit/config/test_settings.py::TestLoadConfig::test_load_config_complex_nested_structure", "unit/config/test_settings.py::TestLoadConfig::test_load_config_from_file", "unit/config/test_settings.py::TestLoadConfig::test_load_config_invalid_yaml", "unit/config/test_settings.py::TestLoadConfig::test_load_config_merge_with_defaults", "unit/config/test_settings.py::TestLoadConfig::test_load_config_none_parameter", "unit/config/test_settings.py::TestLoadConfig::test_load_config_nonexistent_file", "unit/config/test_settings.py::TestLoadConfig::test_load_config_with_environment_override", "unit/config/test_validation.py::TestConfigValidationEdgeCases::test_validate_config_boundary_values", "unit/config/test_validation.py::TestConfigValidationEdgeCases::test_validate_config_with_extra_fields", "unit/config/test_validation.py::TestConfigValidationEdgeCases::test_validate_config_with_nested_extra_fields", "unit/config/test_validation.py::TestConfigValidationEdgeCases::test_validate_empty_config", "unit/config/test_validation.py::TestConfigValidationEdgeCases::test_validate_none_config", "unit/config/test_validation.py::TestConfigValidator::test_validate_api_rate_limiting_settings", "unit/config/test_validation.py::TestConfigValidator::test_validate_boolean_settings", "unit/config/test_validation.py::TestConfigValidator::test_validate_data_settings", "unit/config/test_validation.py::TestConfigValidator::test_validate_exchange_configurations", "unit/config/test_validation.py::TestConfigValidator::test_validate_execution_settings", "unit/config/test_validation.py::TestConfigValidator::test_validate_full_config", "unit/config/test_validation.py::TestConfigValidator::test_validate_invalid_exchange", "unit/config/test_validation.py::TestConfigValidator::test_validate_invalid_strategy_weight", "unit/config/test_validation.py::TestConfigValidator::test_validate_invalid_volatility", "unit/config/test_validation.py::TestConfigValidator::test_validate_minimal_config", "unit/config/test_validation.py::TestConfigValidator::test_validate_missing_exchange", "unit/config/test_validation.py::TestConfigValidator::test_validate_negative_capital", "unit/config/test_validation.py::TestConfigValidator::test_validate_numeric_ranges", "unit/config/test_validation.py::TestConfigValidator::test_validate_portfolio_combination_settings", "unit/config/test_validation.py::TestConfigValidator::test_validate_strategy_configurations", "unit/config/test_validation.py::TestConfigValidator::test_validate_strategy_weights", "unit/config/test_validation.py::TestConfigValidator::test_validate_string_settings", "unit/config/test_validation.py::TestConfigValidator::test_validate_supported_exchanges", "unit/config/test_validation.py::TestConfigValidator::test_validate_trading_cost_adjustment", "unit/data/test_analyzer.py::TestDataAnalyzer::test_adjust_funding_rate_for_trading_costs", "unit/data/test_analyzer.py::TestDataAnalyzer::test_annualize_funding_rate_bybit", "unit/data/test_analyzer.py::TestDataAnalyzer::test_annualize_funding_rate_hyperliquid", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_beta", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_correlation", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_ema", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_log_returns", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_momentum_score", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_returns_from_ohlcv", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_trend_signal", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_volatility_empty_data", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_volatility_from_ohlcv", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_volatility_insufficient_data", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_weighted_volatility", "unit/data/test_analyzer.py::TestDataAnalyzer::test_calculate_z_score", "unit/data/test_analyzer.py::TestDataAnalyzer::test_filter_by_listing_age", "unit/data/test_analyzer.py::TestDataAnalyzer::test_filter_by_volatility", "unit/data/test_analyzer.py::TestDataAnalyzer::test_filter_by_volume", "unit/data/test_analyzer.py::TestDataAnalyzer::test_rank_by_score", "unit/data/test_analyzer.py::TestDataAnalyzer::test_select_top_positions", "unit/data/test_fetcher.py::TestDataFetcher::test_cache_expiration", "unit/data/test_fetcher.py::TestDataFetcher::test_cache_key_generation", "unit/data/test_fetcher.py::TestDataFetcher::test_concurrent_api_calls_limit", "unit/data/test_fetcher.py::TestDataFetcher::test_data_validation", "unit/data/test_fetcher.py::TestDataFetcher::test_error_recovery", "unit/data/test_fetcher.py::TestDataFetcher::test_fetch_funding_rate_success", "unit/data/test_fetcher.py::TestDataFetcher::test_fetch_multiple_symbols_parallel", "unit/data/test_fetcher.py::TestDataFetcher::test_fetch_multiple_timeframes", "unit/data/test_fetcher.py::TestDataFetcher::test_fetch_ohlcv_data_caching", "unit/data/test_fetcher.py::TestDataFetcher::test_fetch_ohlcv_data_success", "unit/data/test_fetcher.py::TestDataFetcher::test_fetch_ohlcv_invalid_symbol", "unit/data/test_fetcher.py::TestDataFetcher::test_fetch_ticker_data_success", "unit/data/test_fetcher.py::TestDataFetcher::test_fetch_ticker_invalid_symbol", "unit/data/test_fetcher.py::TestDataFetcher::test_performance_monitoring", "unit/data/test_fetcher.py::TestDataFetcher::test_rate_limiting", "unit/exchanges/test_base.py::TestExchangeInterface::test_exchange_interface_inheritance", "unit/exchanges/test_base.py::TestExchangeInterface::test_exchange_interface_is_abstract", "unit/exchanges/test_base.py::TestExchangeInterface::test_interface_method_signatures", "unit/exchanges/test_base.py::TestExchangeInterface::test_mock_exchange_implements_interface", "unit/exchanges/test_base.py::TestExchangeInterface::test_required_methods_are_abstract", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_cancel_order_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_create_limit_order_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_create_market_order_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_error_handling_interface", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_fetch_balance_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_fetch_funding_rate_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_fetch_markets_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_fetch_ohlcv_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_fetch_order_book_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_fetch_positions_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_fetch_ticker_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_fetch_tickers_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_initialize_method", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_method_return_types", "unit/exchanges/test_base.py::TestMockExchangeImplementation::test_name_property", "unit/exchanges/test_factory.py::TestExchangeFactory::test_all_supported_exchanges_can_be_created", "unit/exchanges/test_factory.py::TestExchangeFactory::test_create_binance_exchange", "unit/exchanges/test_factory.py::TestExchangeFactory::test_create_bybit_exchange", "unit/exchanges/test_factory.py::TestExchangeFactory::test_create_exchange_case_insensitive", "unit/exchanges/test_factory.py::TestExchangeFactory::test_create_hyperliquid_exchange", "unit/exchanges/test_factory.py::TestExchangeFactory::test_create_okx_exchange", "unit/exchanges/test_factory.py::TestExchangeFactory::test_create_unsupported_exchange", "unit/exchanges/test_factory.py::TestExchangeFactory::test_exchange_interface_compliance", "unit/exchanges/test_factory.py::TestExchangeFactory::test_exchange_name_property_consistency", "unit/exchanges/test_factory.py::TestExchangeFactory::test_factory_error_messages", "unit/exchanges/test_factory.py::TestExchangeFactory::test_factory_is_stateless", "unit/exchanges/test_factory.py::TestExchangeFactory::test_factory_thread_safety", "unit/exchanges/test_factory.py::TestExchangeFactory::test_factory_with_empty_string", "unit/exchanges/test_factory.py::TestExchangeFactory::test_factory_with_none_input", "unit/exchanges/test_factory.py::TestExchangeFactory::test_factory_with_numeric_input", "unit/exchanges/test_factory.py::TestExchangeFactory::test_get_supported_exchanges", "unit/exchanges/test_factory.py::TestExchangeFactory::test_multiple_exchange_types_simultaneously", "unit/exchanges/test_factory.py::TestExchangeFactoryIntegration::test_factory_exchange_configuration_compatibility", "unit/exchanges/test_factory.py::TestExchangeFactoryIntegration::test_factory_with_real_exchange_initialization", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_bybit_8h_negative_funding_rate", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_bybit_8h_positive_funding_rate", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_bounds_checking", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_calculation_consistency", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_edge_cases", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_frequency_consistency", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_mathematical_properties", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_precision", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_sign_preservation", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_hyperliquid_1h_funding_rate", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_trading_cost_adjustment_logic", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateUnitConsistency::test_input_units", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateUnitConsistency::test_output_units", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateUnitConsistency::test_unit_conversion_factors"]