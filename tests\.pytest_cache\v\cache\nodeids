["end_to_end/full_system/test_system_integration.py::TestFullSystemWorkflow::test_complete_trading_workflow_simulation", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_concurrent_operations", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_config_loading_and_validation", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_configuration_edge_cases", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_data_fetching_pipeline", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_error_handling_throughout_system", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_exchange_factory_and_initialization", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_memory_usage_and_cleanup", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_performance_tracking_pipeline", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_portfolio_combination_pipeline", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_strategy_execution_pipeline", "end_to_end/full_system/test_system_integration.py::TestSystemIntegration::test_system_state_consistency", "test_structure_verification.py::TestStructureVerification::test_assertion_helpers_functionality", "test_structure_verification.py::TestStructureVerification::test_can_import_fixtures", "test_structure_verification.py::TestStructureVerification::test_can_import_helpers", "test_structure_verification.py::TestStructureVerification::test_data_generators_functionality", "test_structure_verification.py::TestStructureVerification::test_end_to_end_test_exists", "test_structure_verification.py::TestStructureVerification::test_fixture_files_exist", "test_structure_verification.py::TestStructureVerification::test_helper_files_exist", "test_structure_verification.py::TestStructureVerification::test_mock_exchange_functionality", "test_structure_verification.py::TestStructureVerification::test_pytest_config_exists", "test_structure_verification.py::TestStructureVerification::test_readme_exists", "test_structure_verification.py::TestStructureVerification::test_real_api_test_exists", "test_structure_verification.py::TestStructureVerification::test_sample_unit_test_exists", "test_structure_verification.py::TestStructureVerification::test_test_directories_exist", "test_structure_verification.py::TestStructureVerification::test_test_runner_exists", "test_structure_verification.py::TestStructureVerification::test_unit_test_subdirectories_exist", "test_structure_verification.py::TestStructureVerification::test_validation_test_exists", "test_structure_verification.py::TestTestRunnerFunctionality::test_pytest_markers_are_defined", "test_structure_verification.py::TestTestRunnerFunctionality::test_test_runner_can_be_imported", "unit/config/test_settings.py::TestConfig::test_config_initialization_with_dict", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_bybit_8h_negative_funding_rate", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_bybit_8h_positive_funding_rate", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_bounds_checking", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_calculation_consistency", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_edge_cases", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_frequency_consistency", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_mathematical_properties", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_precision", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_funding_rate_sign_preservation", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_hyperliquid_1h_funding_rate", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateCalculations::test_trading_cost_adjustment_logic", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateUnitConsistency::test_input_units", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateUnitConsistency::test_output_units", "validation/calculations/test_funding_rate_calculations.py::TestFundingRateUnitConsistency::test_unit_conversion_factors"]