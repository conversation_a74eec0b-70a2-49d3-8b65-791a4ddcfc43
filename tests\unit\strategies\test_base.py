"""
Unit tests for strategy base class
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add src to path
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from strategies.base import BaseStrategy, StrategyPosition, StrategyResult
from tests.helpers.mock_exchanges import MockExchange
from tests.fixtures.config_fixtures import STRATEGY_CONFIGS


class ConcreteStrategy(BaseStrategy):
    """Concrete implementation of BaseStrategy for testing"""
    
    def _validate_config(self):
        """Validate strategy configuration"""
        required_params = ['enabled', 'weight']
        for param in required_params:
            if param not in self.config:
                raise ValueError(f"Missing required parameter: {param}")
    
    async def get_universe(self):
        """Get universe of symbols"""
        return ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
    
    async def calculate_features(self, symbols):
        """Calculate features for symbols"""
        features = []
        for symbol in symbols:
            features.append({
                'symbol': symbol,
                'score': 0.5,
                'volatility': 0.2,
                'volume_usd': 1000000
            })
        return features
    
    async def select_positions(self, features):
        """Select positions from features"""
        positions = []
        for feature in features[:2]:  # Select first 2
            positions.append(StrategyPosition(
                symbol=feature['symbol'],
                side='long',
                size=0.1,
                price=50000.0,
                confidence=0.8
            ))
        return positions
    
    async def calculate_position_sizes(self, positions, total_capital):
        """Calculate position sizes"""
        # Simple equal weighting
        weight_per_position = 1.0 / len(positions) if positions else 0
        
        for position in positions:
            position.weight = weight_per_position
            position.notional_usd = total_capital * weight_per_position
            position.size = position.notional_usd / position.price
        
        return positions


class TestStrategyPosition:
    """Test StrategyPosition dataclass"""
    
    def test_strategy_position_creation(self):
        """Test creating StrategyPosition"""
        position = StrategyPosition(
            symbol='BTCUSDT',
            side='long',
            size=0.1,
            price=50000.0,
            confidence=0.8
        )
        
        assert position.symbol == 'BTCUSDT'
        assert position.side == 'long'
        assert position.size == 0.1
        assert position.price == 50000.0
        assert position.confidence == 0.8
        assert position.weight is None  # Default value
        assert position.notional_usd is None  # Default value
    
    def test_strategy_position_with_all_fields(self):
        """Test creating StrategyPosition with all fields"""
        position = StrategyPosition(
            symbol='ETHUSDT',
            side='short',
            size=-1.0,
            price=3000.0,
            confidence=0.9,
            weight=0.5,
            notional_usd=1500.0,
            metadata={'test': 'data'}
        )
        
        assert position.symbol == 'ETHUSDT'
        assert position.side == 'short'
        assert position.size == -1.0
        assert position.price == 3000.0
        assert position.confidence == 0.9
        assert position.weight == 0.5
        assert position.notional_usd == 1500.0
        assert position.metadata == {'test': 'data'}
    
    def test_strategy_position_to_dict(self):
        """Test converting StrategyPosition to dictionary"""
        position = StrategyPosition(
            symbol='BTCUSDT',
            side='long',
            size=0.1,
            price=50000.0,
            confidence=0.8
        )
        
        position_dict = position.to_dict()
        
        assert isinstance(position_dict, dict)
        assert position_dict['symbol'] == 'BTCUSDT'
        assert position_dict['side'] == 'long'
        assert position_dict['size'] == 0.1
        assert position_dict['price'] == 50000.0
        assert position_dict['confidence'] == 0.8


class TestStrategyResult:
    """Test StrategyResult dataclass"""
    
    def test_strategy_result_creation(self):
        """Test creating StrategyResult"""
        positions = [
            StrategyPosition('BTCUSDT', 'long', 0.1, 50000.0, 0.8),
            StrategyPosition('ETHUSDT', 'short', -1.0, 3000.0, 0.7)
        ]
        
        result = StrategyResult(
            strategy_name='test_strategy',
            positions=positions,
            execution_time=1.5,
            metadata={'universe_size': 100}
        )
        
        assert result.strategy_name == 'test_strategy'
        assert len(result.positions) == 2
        assert result.execution_time == 1.5
        assert result.metadata == {'universe_size': 100}
    
    def test_strategy_result_empty_positions(self):
        """Test StrategyResult with empty positions"""
        result = StrategyResult(
            strategy_name='empty_strategy',
            positions=[],
            execution_time=0.5
        )
        
        assert result.strategy_name == 'empty_strategy'
        assert len(result.positions) == 0
        assert result.execution_time == 0.5
        assert result.metadata is None


class TestBaseStrategy:
    """Test BaseStrategy abstract base class"""
    
    @pytest.fixture
    def mock_exchange(self):
        """Create mock exchange"""
        return MockExchange()
    
    @pytest.fixture
    def mock_data_fetcher(self):
        """Create mock data fetcher"""
        return Mock()
    
    @pytest.fixture
    def mock_data_analyzer(self):
        """Create mock data analyzer"""
        return Mock()
    
    @pytest.fixture
    def strategy_config(self):
        """Create strategy configuration"""
        return STRATEGY_CONFIGS['stat_arb_carry_trade']
    
    @pytest.fixture
    async def concrete_strategy(self, strategy_config, mock_data_fetcher, mock_data_analyzer, mock_exchange):
        """Create concrete strategy instance"""
        await mock_exchange.initialize({})
        return ConcreteStrategy(
            'test_strategy',
            strategy_config,
            mock_data_fetcher,
            mock_data_analyzer,
            mock_exchange
        )
    
    def test_base_strategy_is_abstract(self):
        """Test that BaseStrategy cannot be instantiated directly"""
        with pytest.raises(TypeError):
            BaseStrategy('test', {}, None, None, None)
    
    def test_concrete_strategy_initialization(self, concrete_strategy):
        """Test concrete strategy initialization"""
        assert concrete_strategy.strategy_name == 'test_strategy'
        assert concrete_strategy.config is not None
        assert concrete_strategy.data_fetcher is not None
        assert concrete_strategy.data_analyzer is not None
        assert concrete_strategy.exchange is not None
        assert hasattr(concrete_strategy, 'logger')
    
    def test_strategy_config_validation(self, mock_data_fetcher, mock_data_analyzer, mock_exchange):
        """Test strategy configuration validation"""
        # Valid config should work
        valid_config = {'enabled': True, 'weight': 0.5}
        strategy = ConcreteStrategy(
            'test_strategy',
            valid_config,
            mock_data_fetcher,
            mock_data_analyzer,
            mock_exchange
        )
        assert strategy.config == valid_config
        
        # Invalid config should raise error
        invalid_config = {'enabled': True}  # Missing weight
        with pytest.raises(ValueError, match="Missing required parameter: weight"):
            ConcreteStrategy(
                'test_strategy',
                invalid_config,
                mock_data_fetcher,
                mock_data_analyzer,
                mock_exchange
            )
    
    @pytest.mark.asyncio
    async def test_get_universe_method(self, concrete_strategy):
        """Test get_universe method"""
        universe = await concrete_strategy.get_universe()
        
        assert isinstance(universe, list)
        assert len(universe) > 0
        assert all(isinstance(symbol, str) for symbol in universe)
        assert 'BTCUSDT' in universe
    
    @pytest.mark.asyncio
    async def test_calculate_features_method(self, concrete_strategy):
        """Test calculate_features method"""
        symbols = ['BTCUSDT', 'ETHUSDT']
        features = await concrete_strategy.calculate_features(symbols)
        
        assert isinstance(features, list)
        assert len(features) == len(symbols)
        
        for i, feature in enumerate(features):
            assert isinstance(feature, dict)
            assert feature['symbol'] == symbols[i]
            assert 'score' in feature
            assert 'volatility' in feature
            assert 'volume_usd' in feature
    
    @pytest.mark.asyncio
    async def test_select_positions_method(self, concrete_strategy):
        """Test select_positions method"""
        features = [
            {'symbol': 'BTCUSDT', 'score': 0.8},
            {'symbol': 'ETHUSDT', 'score': 0.6},
            {'symbol': 'ADAUSDT', 'score': 0.4}
        ]
        
        positions = await concrete_strategy.select_positions(features)
        
        assert isinstance(positions, list)
        assert len(positions) == 2  # ConcreteStrategy selects first 2
        
        for position in positions:
            assert isinstance(position, StrategyPosition)
            assert position.symbol in ['BTCUSDT', 'ETHUSDT']
            assert position.side == 'long'
            assert position.size > 0
            assert position.price > 0
            assert 0 <= position.confidence <= 1
    
    @pytest.mark.asyncio
    async def test_calculate_position_sizes_method(self, concrete_strategy):
        """Test calculate_position_sizes method"""
        positions = [
            StrategyPosition('BTCUSDT', 'long', 0.1, 50000.0, 0.8),
            StrategyPosition('ETHUSDT', 'short', -1.0, 3000.0, 0.7)
        ]
        
        total_capital = 10000.0
        sized_positions = await concrete_strategy.calculate_position_sizes(positions, total_capital)
        
        assert len(sized_positions) == 2
        
        for position in sized_positions:
            assert position.weight is not None
            assert position.notional_usd is not None
            assert position.weight == 0.5  # Equal weighting
            assert position.notional_usd == 5000.0  # Half of total capital
    
    @pytest.mark.asyncio
    async def test_execute_strategy_workflow(self, concrete_strategy):
        """Test complete strategy execution workflow"""
        result = await concrete_strategy.execute()
        
        assert isinstance(result, StrategyResult)
        assert result.strategy_name == 'test_strategy'
        assert isinstance(result.positions, list)
        assert isinstance(result.execution_time, float)
        assert result.execution_time > 0
        
        # Check positions have proper sizing
        for position in result.positions:
            assert isinstance(position, StrategyPosition)
            assert position.weight is not None
            assert position.notional_usd is not None
    
    @pytest.mark.asyncio
    async def test_strategy_execution_timing(self, concrete_strategy):
        """Test that strategy execution timing is recorded"""
        import time
        
        start_time = time.time()
        result = await concrete_strategy.execute()
        end_time = time.time()
        
        actual_time = end_time - start_time
        recorded_time = result.execution_time
        
        # Recorded time should be close to actual time
        assert abs(recorded_time - actual_time) < 0.1  # Within 100ms
    
    @pytest.mark.asyncio
    async def test_strategy_with_empty_universe(self, concrete_strategy):
        """Test strategy behavior with empty universe"""
        # Mock get_universe to return empty list
        concrete_strategy.get_universe = AsyncMock(return_value=[])
        
        result = await concrete_strategy.execute()
        
        assert isinstance(result, StrategyResult)
        assert len(result.positions) == 0
    
    @pytest.mark.asyncio
    async def test_strategy_with_no_selected_positions(self, concrete_strategy):
        """Test strategy behavior when no positions are selected"""
        # Mock select_positions to return empty list
        concrete_strategy.select_positions = AsyncMock(return_value=[])
        
        result = await concrete_strategy.execute()
        
        assert isinstance(result, StrategyResult)
        assert len(result.positions) == 0
    
    @pytest.mark.asyncio
    async def test_strategy_error_handling(self, concrete_strategy):
        """Test strategy error handling"""
        # Mock get_universe to raise exception
        concrete_strategy.get_universe = AsyncMock(side_effect=Exception("Test error"))
        
        with pytest.raises(Exception, match="Test error"):
            await concrete_strategy.execute()
    
    def test_strategy_logger_setup(self, concrete_strategy):
        """Test that strategy logger is properly set up"""
        assert hasattr(concrete_strategy, 'logger')
        assert concrete_strategy.logger.name.endswith('test_strategy')
    
    @pytest.mark.asyncio
    async def test_strategy_metadata_collection(self, concrete_strategy):
        """Test that strategy collects execution metadata"""
        result = await concrete_strategy.execute()
        
        # Should have basic execution metadata
        assert isinstance(result.execution_time, float)
        assert result.execution_time > 0
        
        # Strategy name should be recorded
        assert result.strategy_name == 'test_strategy'
    
    @pytest.mark.asyncio
    async def test_multiple_strategy_executions(self, concrete_strategy):
        """Test multiple executions of same strategy"""
        results = []
        
        for _ in range(3):
            result = await concrete_strategy.execute()
            results.append(result)
        
        # All executions should succeed
        assert len(results) == 3
        
        for result in results:
            assert isinstance(result, StrategyResult)
            assert result.strategy_name == 'test_strategy'
    
    @pytest.mark.asyncio
    async def test_strategy_position_validation(self, concrete_strategy):
        """Test that strategy validates position data"""
        result = await concrete_strategy.execute()
        
        for position in result.positions:
            # Basic validation
            assert isinstance(position.symbol, str)
            assert position.side in ['long', 'short']
            assert isinstance(position.size, (int, float))
            assert isinstance(position.price, (int, float))
            assert isinstance(position.confidence, (int, float))
            assert 0 <= position.confidence <= 1
            
            # Size validation
            if position.side == 'long':
                assert position.size > 0
            elif position.side == 'short':
                assert position.size < 0
            
            # Price validation
            assert position.price > 0


if __name__ == '__main__':
    pytest.main([__file__])
