"""
Configuration fixtures for testing
"""

# Minimal valid configuration
MINIMAL_CONFIG = {
    "exchange": "bybit",
    "use_demo": True,
    "use_testnet": True,
    "total_capital_usd": 1000,
    "simulation_mode": True,
    "strategies": {
        "stat_arb_carry_trade": {
            "enabled": True,
            "weight": 1.0
        }
    }
}

# Full configuration with all parameters
FULL_CONFIG = {
    # Exchange settings
    "exchange": "bybit",
    "api_key": "test_key",
    "api_secret": "test_secret",
    "use_testnet": True,
    "use_demo": True,
    
    # Strategy configuration
    "strategies": {
        "stat_arb_carry_trade": {
            "enabled": True,
            "weight": 0.33
        },
        "cross_sectional_momentum": {
            "enabled": True,
            "weight": 0.33
        },
        "trend_trading": {
            "enabled": True,
            "weight": 0.34
        }
    },
    
    # Portfolio settings
    "total_capital_usd": 10000,
    "simulation_mode": True,
    "immediate_start": True,
    "debug_mode": False,
    
    # Portfolio combination
    "portfolio_combination": {
        "enable_position_netting": True,
        "min_position_size_usd": 10.0,
        "position_rounding_decimals": 6,
        "enable_portfolio_vol_targeting": True,
        "portfolio_target_volatility": 0.20,
        "default_correlation": 0.30
    },
    
    # Risk management
    "buffer_zone_tolerance_percentage": 5.0,
    "enable_beta_projection": False,
    "market_index_symbol": "BTCUSDT",
    "beta_neutrality_tolerance": 0.05,
    
    # Execution settings
    "preferred_execution_style": "conservative",
    "min_batch_size": 3,
    "max_batch_size": 5,
    "min_batch_interval_seconds": 60,
    "max_batch_interval_seconds": 300,
    "min_orderbook_levels": 3,
    "max_orderbook_levels": 6,
    
    # Data settings
    "exclude_new_listings_days": 60,
    "min_historical_data_days": 60,
    "min_orderbook_depth": 3,
    "max_spread_threshold": 0.05,
    
    # Performance tracking
    "performance_tracking": {
        "max_performance_history_days": 90,
        "enable_detailed_performance_tracking": True,
        "performance_tracking_file": "performance_history.json"
    },
    
    # Database configuration
    "database": {
        "path": "data/trading_system.db",
        "enable_wal_mode": True,
        "cache_size_mb": 64,
        "vacuum_frequency_days": 30,
        "enable_integrity_checks": True
    },
    
    # Basic metrics
    "basic_metrics": {
        "enable_basic_metrics": True,
        "risk_free_rate": 0.02,
        "metrics_calculation_frequency_hours": 6,
        "enable_real_time_tracking": True,
        "track_pnl_curve": True,
        "track_sharpe_ratio": True,
        "track_calmar_ratio": True,
        "track_annualized_returns": True,
        "track_max_drawdown": True,
        "track_max_recovery_time": True,
        "track_portfolio_exposures": True,
        "track_performance_attribution": True,
        "track_turnover": True,
        "track_capacity_utilization": True,
        "track_slippage": True
    },
    
    # Rate limiting
    "api_rate_limiting": {
        "max_requests_per_second": 5,
        "max_concurrent_requests": 3,
        "burst_limit": 10,
        "order_placement": {
            "max_orders_per_second": 3,
            "max_orders_per_minute": 120,
            "batch_delay_ms": 500,
            "retry_delay_ms": 1500,
            "max_retries": 3
        }
    },
    
    # CCXT configuration
    "ccxt_config": {
        "enable_rate_limit": True,
        "timeout": 30000,
        "rateLimit": 500
    }
}

# Invalid configurations for testing validation
INVALID_CONFIGS = {
    "missing_exchange": {
        "total_capital_usd": 1000,
        "simulation_mode": True
    },
    
    "invalid_exchange": {
        "exchange": "invalid_exchange",
        "total_capital_usd": 1000,
        "simulation_mode": True
    },
    
    "negative_capital": {
        "exchange": "bybit",
        "total_capital_usd": -1000,
        "simulation_mode": True
    },
    
    "invalid_strategy_weight": {
        "exchange": "bybit",
        "total_capital_usd": 1000,
        "simulation_mode": True,
        "strategies": {
            "stat_arb_carry_trade": {
                "enabled": True,
                "weight": -0.5  # Invalid negative weight
            }
        }
    },
    
    "missing_required_fields": {
        "exchange": "bybit"
        # Missing total_capital_usd and other required fields
    },
    
    "invalid_volatility": {
        "exchange": "bybit",
        "total_capital_usd": 1000,
        "simulation_mode": True,
        "portfolio_combination": {
            "portfolio_target_volatility": -0.1  # Invalid negative volatility
        }
    }
}

# Strategy-specific configurations
STRATEGY_CONFIGS = {
    "stat_arb_carry_trade": {
        "enabled": True,
        "weight": 1.0,
        "max_positions_per_leg": 5,
        "min_daily_volume_usd": 1000000,
        "min_volatility_threshold": 0.05,
        "target_volatility": 0.20,
        "trading_cost_adjustment": 0.1095,
        "enable_linear_decay_weighting": True,
        "enable_volatility_targeting": True,
        "enable_contract_compliance": True,
        "min_listing_age_days": 90,
        "enable_beta_projection": False
    },
    
    "cross_sectional_momentum": {
        "enabled": True,
        "weight": 1.0,
        "universe_size": 50,
        "lookback_days": 20,
        "min_daily_volume_usd": 1000000,
        "min_volatility_threshold": 0.05,
        "target_volatility": 0.20,
        "enable_linear_decay_weighting": True,
        "enable_volatility_targeting": True,
        "enable_contract_compliance": True,
        "min_listing_age_days": 60,
        "enable_beta_projection": False,
        "allow_shorts_on_negative_signals": True
    },
    
    "trend_trading": {
        "enabled": True,
        "weight": 1.0,
        "ema_short_period": 12,
        "ema_long_period": 26,
        "min_daily_volume_usd": 1000000,
        "min_volatility_threshold": 0.05,
        "target_volatility": 0.20,
        "enable_linear_decay_weighting": True,
        "enable_volatility_targeting": True,
        "enable_contract_compliance": True,
        "min_listing_age_days": 60,
        "enable_beta_projection": False,
        "use_log_prices": True
    }
}

# Exchange-specific configurations
EXCHANGE_CONFIGS = {
    "bybit": {
        "exchange": "bybit",
        "use_demo": True,
        "use_testnet": True,
        "api_key": "test_bybit_key",
        "api_secret": "test_bybit_secret",
        "ccxt_config": {
            "bybit_options": {
                "defaultType": "swap",
                "recvWindow": 20000,
                "adjustForTimeDifference": True,
                "rateLimit": 200
            }
        }
    },
    
    "binance": {
        "exchange": "binance",
        "use_demo": False,
        "use_testnet": True,
        "api_key": "test_binance_key",
        "api_secret": "test_binance_secret",
        "ccxt_config": {
            "binance_options": {
                "defaultType": "future",
                "recvWindow": 5000,
                "adjustForTimeDifference": True,
                "rateLimit": 100
            }
        }
    },
    
    "okx": {
        "exchange": "okx",
        "use_demo": False,
        "use_testnet": True,
        "api_key": "test_okx_key",
        "api_secret": "test_okx_secret",
        "passphrase": "test_okx_passphrase",
        "ccxt_config": {
            "okx_options": {
                "defaultType": "swap",
                "recvWindow": 20000,
                "adjustForTimeDifference": True,
                "rateLimit": 150
            }
        }
    },
    
    "hyperliquid": {
        "exchange": "hyperliquid",
        "use_demo": False,
        "use_testnet": True,
        "wallet_address": "******************************************",
        "private_key": "test_private_key",
        "ccxt_config": {
            "hyperliquid_options": {
                "rateLimit": 100,
                "timeout": 30000
            }
        }
    }
}

# Test configuration combinations
TEST_CONFIG_COMBINATIONS = {
    "single_strategy_bybit": {
        **MINIMAL_CONFIG,
        "exchange": "bybit",
        "strategies": {
            "stat_arb_carry_trade": {"enabled": True, "weight": 1.0}
        }
    },
    
    "multi_strategy_bybit": {
        **MINIMAL_CONFIG,
        "exchange": "bybit",
        "total_capital_usd": 10000,
        "strategies": {
            "stat_arb_carry_trade": {"enabled": True, "weight": 0.4},
            "cross_sectional_momentum": {"enabled": True, "weight": 0.3},
            "trend_trading": {"enabled": True, "weight": 0.3}
        }
    },
    
    "high_capital_conservative": {
        **FULL_CONFIG,
        "total_capital_usd": 100000,
        "preferred_execution_style": "conservative",
        "portfolio_combination": {
            "enable_position_netting": True,
            "portfolio_target_volatility": 0.15  # Lower volatility target
        }
    },
    
    "aggressive_execution": {
        **FULL_CONFIG,
        "preferred_execution_style": "aggressive",
        "min_batch_interval_seconds": 30,
        "max_batch_interval_seconds": 120,
        "api_rate_limiting": {
            "max_requests_per_second": 10,
            "max_concurrent_requests": 5
        }
    }
}
