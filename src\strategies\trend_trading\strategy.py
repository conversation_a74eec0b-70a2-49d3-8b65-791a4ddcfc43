"""
Trend Trading Strategy Implementation

This strategy implements time-series trend following by:
1. Selecting universe of top 50 coins by market cap with volume/volatility filters
2. Calculating momentum features using multiple EMA timeframes
3. Using time-series approach where all coins get positions based on individual signals
4. Applying sigmoid weighting + equal capital allocation + volatility targeting
"""

import logging
import math
import numpy as np
import asyncio
from typing import Dict, List, Optional, Any, Tuple
import yaml
from pathlib import Path

from strategies.base import BaseStrategy, StrategyPosition
from utils.data_validation import DataUnitValidator

logger = logging.getLogger(__name__)


class TrendTradingStrategy(BaseStrategy):
    """
    Trend Trading Strategy

    Implements time-series trend following using momentum signals from multiple EMA timeframes,
    sigmoid weighting, and equal capital allocation with volatility targeting.
    """

    def __init__(self, strategy_name: str, config: Dict[str, Any], 
                 data_fetcher, data_analyzer, exchange):
        """Initialize the trend trading strategy"""
        super().__init__(strategy_name, config, data_fetcher, data_analyzer, exchange)
        
        # Load strategy-specific configuration
        self._load_strategy_config()
        
        # Extract commonly used config values
        self.total_capital = self.config.get('total_capital_usd', 10000)
        self.exchange_name = exchange.name if hasattr(exchange, 'name') else str(exchange)
        
        self.logger.info(f"🏗️ Trend Trading Strategy initialized with ${self.total_capital:,.0f} capital")

    def _validate_config(self) -> None:
        """Validate strategy-specific configuration parameters"""
        required_params = [
            'top_market_cap_count',
            'min_daily_volume_usd', 
            'min_historical_data_days',
            'ema_timeframes',
            'target_volatility'
        ]
        
        for param in required_params:
            if param not in self.config:
                raise ValueError(f"Missing required config parameter: {param}")
        
        # Validate EMA timeframes
        ema_timeframes = self.config.get('ema_timeframes', [])
        if not ema_timeframes or not isinstance(ema_timeframes, list):
            raise ValueError("ema_timeframes must be a non-empty list")
        
        for timeframe in ema_timeframes:
            if not isinstance(timeframe, list) or len(timeframe) != 2:
                raise ValueError(f"Invalid EMA timeframe format: {timeframe}. Expected [short, long]")
            if timeframe[0] >= timeframe[1]:
                raise ValueError(f"Invalid EMA timeframe: {timeframe}. Short period must be less than long period")

    def _load_strategy_config(self) -> None:
        """Load strategy-specific configuration from config file"""
        try:
            config_path = Path(__file__).parent / "config.yaml"
            if config_path.exists():
                with open(config_path, 'r') as f:
                    strategy_config = yaml.safe_load(f)
                
                # Merge strategy config with provided config (provided config takes precedence)
                for key, value in strategy_config.items():
                    if key not in self.config:
                        self.config[key] = value
                
                self.logger.info(f"✅ Loaded strategy configuration from {config_path}")
            else:
                self.logger.warning(f"⚠️ Strategy config file not found: {config_path}")
                
        except Exception as e:
            self.logger.error(f"❌ Failed to load strategy configuration: {e}")
            # Continue with provided config only

    async def get_universe(self) -> List[str]:
        """
        Get the universe of symbols for trend trading strategy
        
        Universe selection:
        - Top 50 coins by market cap (using CoinGecko API)
        - Perpetual futures only (USDT pairs)
        - Volume threshold: $3M minimum daily volume (30-day average)
        - Volatility filters: Annualized weighted volatility above 5%
        - Historical data: Minimum 150 days of close price history required
        - Exclusions: Stablecoins, wrapped coins
        
        Returns:
            List of symbol strings (e.g., ['BTCUSDT', 'ETHUSDT'])
        """
        try:
            self.logger.info("🌍 Getting trend trading universe...")
            
            # Target universe size
            target_count = self.config.get('top_market_cap_count', 50)

            # Start with a larger pool to ensure we get exactly target_count after filtering
            initial_limit = target_count * 3  # Pull 3x more coins initially

            symbols = await self.data_fetcher.get_top_market_cap_symbols(
                limit=initial_limit,
                exclude_stablecoins=self.config.get('exclude_stablecoins', True),
                exclude_wrapped=self.config.get('exclude_wrapped_coins', True)
            )

            if not symbols:
                self.logger.warning("⚠️ No symbols returned from market cap filter")
                return []

            self.logger.info(f"📊 Got {len(symbols)} symbols from market cap filter (target: {target_count})")

            # Apply filters sequentially until we have exactly target_count symbols
            filtered_symbols = symbols

            # Apply volume filter
            min_volume = self.config.get('min_daily_volume_usd', 3000000)
            volume_filtered = await self.data_fetcher.filter_by_volume(
                filtered_symbols, min_daily_volume_usd=min_volume
            )
            self.logger.info(f"📊 {len(volume_filtered)} symbols passed volume filter (${min_volume:,.0f})")

            # Apply volatility filter
            min_volatility = self.config.get('min_volatility_threshold', 0.05)
            volatility_filtered = await self.data_fetcher.filter_by_volatility(
                volume_filtered, min_volatility=min_volatility
            )
            self.logger.info(f"📊 {len(volatility_filtered)} symbols passed volatility filter ({min_volatility:.1%})")

            # Apply historical data filter
            min_history_days = self.config.get('min_historical_data_days', 150)
            history_filtered = await self.data_fetcher.filter_by_historical_data(
                volatility_filtered, min_days=min_history_days
            )
            self.logger.info(f"📊 {len(history_filtered)} symbols passed historical data filter ({min_history_days} days)")

            # Apply new listings filter
            exclude_new_days = self.config.get('exclude_new_listings_days', 60)
            if exclude_new_days > 0:
                new_listings_filtered = await self.data_fetcher.filter_new_listings(
                    history_filtered, exclude_days=exclude_new_days
                )
                self.logger.info(f"📊 {len(new_listings_filtered)} symbols passed new listings filter ({exclude_new_days} days)")
            else:
                new_listings_filtered = history_filtered

            # Take exactly target_count symbols (top by market cap order)
            final_universe = new_listings_filtered[:target_count]

            self.logger.info(f"✅ Final universe: exactly {len(final_universe)} symbols (target: {target_count})")
            
            if self.config.get('log_position_selection_details', False):
                self.logger.info(f"🎯 Universe symbols: {final_universe}")
            
            return final_universe
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get universe: {e}")
            raise

    async def calculate_features(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """
        Calculate strategy-specific features for the given symbols

        Features calculated:
        1. Weighted volatility (0.3×60d + 0.5×30d + 0.2×10d)
        2. Momentum signals from multiple EMA timeframes
        3. Combined trend signal (equal weight combination)

        Note: No beta calculation since this is a time-series strategy

        Args:
            symbols: List of symbols to calculate features for

        Returns:
            List of dictionaries containing symbol data with calculated features
        """
        try:
            self.logger.info(f"📊 Calculating features for {len(symbols)} symbols...")

            if not symbols:
                self.logger.warning("⚠️ No symbols provided for feature calculation")
                return []

            # Fetch OHLCV data for all symbols
            self.logger.info("📈 Fetching OHLCV data...")
            ohlcv_data = await self.data_fetcher.get_ohlcv_batch(
                symbols,
                timeframe='1d',
                limit=200  # Need enough data for longest EMA (128 periods) + buffer
            )

            if not ohlcv_data:
                self.logger.warning("⚠️ No OHLCV data received")
                return []

            enriched_symbols = []

            # Process symbols in batches to manage memory and API limits
            batch_size = self.config.get('symbol_processing_batch_size', 10)

            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]
                self.logger.info(f"🔄 Processing batch {i//batch_size + 1}/{(len(symbols)-1)//batch_size + 1}: {len(batch_symbols)} symbols")

                batch_results = await self._process_symbol_batch(batch_symbols, ohlcv_data)
                enriched_symbols.extend(batch_results)

                # Small delay between batches to be conservative with API usage
                if i + batch_size < len(symbols):
                    await asyncio.sleep(0.1)

            # Filter out symbols that failed feature calculation
            valid_symbols = [s for s in enriched_symbols if s.get('trend_signal') is not None]

            self.logger.info(f"✅ Feature calculation complete: {len(valid_symbols)}/{len(symbols)} symbols successful")

            if self.config.get('log_feature_calculations', False):
                for symbol_data in valid_symbols[:5]:  # Log first 5 for debugging
                    self.logger.info(f"📊 {symbol_data['symbol']}: trend={symbol_data['trend_signal']:.4f}, "
                                   f"vol={symbol_data['weighted_volatility']:.3f}")

            return valid_symbols

        except Exception as e:
            self.logger.error(f"❌ Failed to calculate features: {e}")
            raise

    async def _process_symbol_batch(self, symbols: List[str], ohlcv_data: Dict) -> List[Dict[str, Any]]:
        """Process a batch of symbols for feature calculation"""
        batch_results = []

        for symbol in symbols:
            try:
                symbol_data = await self._calculate_symbol_features(symbol, ohlcv_data.get(symbol))
                if symbol_data:
                    batch_results.append(symbol_data)

            except Exception as e:
                self.logger.warning(f"⚠️ Failed to calculate features for {symbol}: {e}")
                if not self.config.get('skip_problematic_symbols', True):
                    raise
                continue

        return batch_results

    async def _calculate_symbol_features(self, symbol: str, ohlcv: Optional[List]) -> Optional[Dict[str, Any]]:
        """Calculate features for a single symbol"""
        try:
            if not ohlcv or len(ohlcv) < 150:  # Need minimum historical data
                self.logger.warning(f"⚠️ Insufficient data for {symbol}: {len(ohlcv) if ohlcv else 0} periods")
                return None

            # Convert OHLCV to numpy arrays for efficient calculation
            closes = np.array([float(candle[4]) for candle in ohlcv])  # Close prices

            if len(closes) < 150:
                return None

            # Calculate weighted volatility
            weighted_volatility = self._calculate_weighted_volatility(closes)

            # Calculate momentum signals from multiple EMA timeframes
            trend_signal = self._calculate_trend_signal(closes, weighted_volatility)

            # Get current price and volume
            current_price = float(ohlcv[-1][4])  # Latest close price
            current_volume = float(ohlcv[-1][5])  # Latest volume

            # Validate calculations
            if not DataUnitValidator.validate_price_data(symbol, current_price):
                self.logger.warning(f"⚠️ Invalid price data for {symbol}: {current_price}")
                return None

            if not DataUnitValidator.validate_volatility_data(symbol, weighted_volatility):
                self.logger.warning(f"⚠️ Invalid volatility data for {symbol}: {weighted_volatility}")
                return None

            return {
                'symbol': symbol,
                'price': current_price,
                'last_price': current_price,
                'volume': current_volume,
                'weighted_volatility': weighted_volatility,
                'trend_signal': trend_signal,
                'data_quality': 'valid',
                'calculation_timestamp': asyncio.get_event_loop().time()
            }

        except Exception as e:
            self.logger.error(f"❌ Error calculating features for {symbol}: {e}")
            return None

    def _calculate_weighted_volatility(self, closes: np.ndarray) -> float:
        """
        Calculate weighted volatility using multiple timeframes

        Formula: weighted_volatility = (0.3 × vol_60d) + (0.5 × vol_30d) + (0.2 × vol_10d)
        Annualized volatility calculation (365-day basis for crypto 24/7 trading)

        Args:
            closes: Array of close prices

        Returns:
            Weighted volatility (annualized)
        """
        try:
            if len(closes) < 60:
                return self.config.get('default_volatility', 0.20)

            # Calculate daily returns
            returns = np.diff(np.log(closes))

            # Calculate volatilities for different periods
            vol_10d = np.std(returns[-10:]) * np.sqrt(365) if len(returns) >= 10 else 0
            vol_30d = np.std(returns[-30:]) * np.sqrt(365) if len(returns) >= 30 else 0
            vol_60d = np.std(returns[-60:]) * np.sqrt(365) if len(returns) >= 60 else 0

            # Get weights from config
            weights = self.config.get('volatility_weights', {
                'vol_60d': 0.3,
                'vol_30d': 0.5,
                'vol_10d': 0.2
            })

            # Calculate weighted volatility
            weighted_vol = (
                weights['vol_60d'] * vol_60d +
                weights['vol_30d'] * vol_30d +
                weights['vol_10d'] * vol_10d
            )

            # Ensure minimum volatility
            min_vol = self.config.get('min_volatility_threshold', 0.05)
            weighted_vol = max(weighted_vol, min_vol)

            return weighted_vol

        except Exception as e:
            self.logger.warning(f"⚠️ Error calculating weighted volatility: {e}")
            return self.config.get('default_volatility', 0.20)

    def _calculate_trend_signal(self, closes: np.ndarray, weighted_volatility: float) -> float:
        """
        Calculate trend signal from multiple EMA timeframes using log prices

        Process:
        1. Convert to log prices for cleaner mathematics
        2. Calculate EMA pairs: 2/8, 4/16, 8/32, 16/64, 32/128 periods
        3. Signal formula for each timeframe: (short_ema - long_ema) / lookback_volatility
        4. Use volatility of lookback equal to long EMA period for each pair
        5. Equal weight combination: trend_signal = sum(all_timeframe_signals) / 5

        Args:
            closes: Array of close prices
            weighted_volatility: Weighted volatility (kept for fallback)

        Returns:
            Combined trend signal
        """
        try:
            if len(closes) < 128:  # Need enough data for longest EMA
                return self.config.get('default_trend_signal', 0.0)

            # Convert to log prices for cleaner mathematics
            log_prices = np.log(closes)

            ema_timeframes = self.config.get('ema_timeframes', [
                [2, 8], [4, 16], [8, 32], [16, 64], [32, 128]
            ])

            timeframe_signals = []

            for short_period, long_period in ema_timeframes:
                try:
                    # Calculate EMAs on log prices
                    short_ema = self._calculate_ema(log_prices, short_period)
                    long_ema = self._calculate_ema(log_prices, long_period)

                    if short_ema is None or long_ema is None:
                        continue

                    # Calculate volatility for lookback period equal to long EMA period
                    lookback_volatility = self._calculate_lookback_volatility(log_prices, long_period)

                    if lookback_volatility <= 0:
                        # Fallback to weighted volatility if lookback calculation fails
                        lookback_volatility = weighted_volatility

                    # Calculate signal: (short_ema - long_ema) / lookback_volatility
                    signal = (short_ema - long_ema) / lookback_volatility

                    timeframe_signals.append(signal)

                except Exception as e:
                    self.logger.warning(f"⚠️ Error calculating EMA signal for {short_period}/{long_period}: {e}")
                    continue

            if not timeframe_signals:
                return self.config.get('default_trend_signal', 0.0)

            # Equal weight combination
            if self.config.get('equal_weight_combination', True):
                trend_signal = sum(timeframe_signals) / len(timeframe_signals)
            else:
                # Could implement custom weighting here if needed
                trend_signal = sum(timeframe_signals) / len(timeframe_signals)

            return trend_signal

        except Exception as e:
            self.logger.warning(f"⚠️ Error calculating trend signal: {e}")
            return self.config.get('default_trend_signal', 0.0)

    def _calculate_lookback_volatility(self, log_prices: np.ndarray, lookback_period: int) -> float:
        """
        Calculate volatility for a specific lookback period

        Args:
            log_prices: Array of log prices
            lookback_period: Number of periods to look back

        Returns:
            Annualized volatility for the lookback period
        """
        try:
            if len(log_prices) < lookback_period + 1:
                return self.config.get('default_volatility', 0.20)

            # Calculate returns for the lookback period
            recent_log_prices = log_prices[-lookback_period-1:]
            returns = np.diff(recent_log_prices)

            if len(returns) == 0:
                return self.config.get('default_volatility', 0.20)

            # Calculate volatility and annualize (365-day basis for crypto)
            volatility = np.std(returns) * np.sqrt(365)

            # Ensure minimum volatility
            min_vol = self.config.get('min_volatility_threshold', 0.05)
            volatility = max(volatility, min_vol)

            return volatility

        except Exception as e:
            self.logger.warning(f"⚠️ Error calculating lookback volatility for period {lookback_period}: {e}")
            return self.config.get('default_volatility', 0.20)

    def _calculate_ema(self, prices: np.ndarray, period: int) -> Optional[float]:
        """
        Calculate Exponential Moving Average

        Args:
            prices: Array of prices (can be regular prices or log prices)
            period: EMA period

        Returns:
            Current EMA value or None if calculation fails
        """
        try:
            if len(prices) < period:
                return None

            # Calculate EMA using pandas-like approach
            alpha = 2.0 / (period + 1)
            ema = prices[0]  # Start with first price

            for price in prices[1:]:
                ema = alpha * price + (1 - alpha) * ema

            return ema

        except Exception as e:
            self.logger.warning(f"⚠️ Error calculating EMA for period {period}: {e}")
            return None

    async def select_positions(self, enriched_symbols: List[Dict[str, Any]]) -> Tuple[List[Dict], List[Dict]]:
        """
        Select position candidates using time-series approach

        Time-series strategy approach:
        - Each coin is evaluated individually against its own historical data
        - No cross-sectional ranking between coins
        - All coins with valid signals get positions
        - Position direction based on trend signal sign:
          * Positive trend signal = long position
          * Negative trend signal = short position
          * Zero/neutral trend signal = no position

        This differs from cross-sectional strategies that rank coins against each other
        and select only top N performers.

        Args:
            enriched_symbols: Symbols with calculated features

        Returns:
            Tuple of (long_candidates, short_candidates)
        """
        try:
            self.logger.info(f"🎯 Selecting positions from {len(enriched_symbols)} enriched symbols (time-series approach)...")

            if not enriched_symbols:
                self.logger.warning("⚠️ No enriched symbols for position selection")
                return [], []

            # Filter symbols with valid trend signals
            valid_symbols = [s for s in enriched_symbols if s.get('trend_signal') is not None]

            if not valid_symbols:
                self.logger.warning("⚠️ No symbols with valid trend signals")
                return [], []

            long_candidates = []
            short_candidates = []
            neutral_count = 0

            # Time-series approach: evaluate each symbol individually
            for symbol_data in valid_symbols:
                trend_signal = symbol_data['trend_signal']

                # Position direction based on signal sign
                if trend_signal > 0:
                    # Positive signal = long position
                    long_candidates.append(symbol_data)
                elif trend_signal < 0:
                    # Negative signal = short position
                    short_candidates.append(symbol_data)
                else:
                    # Zero/neutral signal = no position
                    neutral_count += 1

            self.logger.info(f"✅ Time-series position selection complete:")
            self.logger.info(f"   Long candidates: {len(long_candidates)}")
            self.logger.info(f"   Short candidates: {len(short_candidates)}")
            self.logger.info(f"   Neutral (no position): {neutral_count}")

            if self.config.get('log_position_selection_details', False):
                self.logger.info("📊 Long candidates:")
                for candidate in long_candidates:
                    self.logger.info(f"   {candidate['symbol']}: trend={candidate['trend_signal']:.4f}")

                self.logger.info("📊 Short candidates:")
                for candidate in short_candidates:
                    self.logger.info(f"   {candidate['symbol']}: trend={candidate['trend_signal']:.4f}")

            return long_candidates, short_candidates

        except Exception as e:
            self.logger.error(f"❌ Failed to select positions: {e}")
            raise

    async def size_positions(self, long_candidates: List[Dict],
                           short_candidates: List[Dict]) -> List[StrategyPosition]:
        """
        Calculate position sizes using sigmoid weighting and equal capital allocation

        Sizing methodology:
        1. Apply sigmoid weighting: tanh(2*trend_signal) to convert signals to base weights
        2. Apply equal capital allocation: multiply by 1/N for equal capital allocation
        3. Apply volatility targeting: adjust by target_volatility / asset_volatility
        4. Contract compliance and price validation
        5. Beta projection disabled (since this is time-series, not cross-sectional)

        Args:
            long_candidates: Selected long position candidates
            short_candidates: Selected short position candidates

        Returns:
            List of StrategyPosition objects with calculated sizes
        """
        try:
            self.logger.info(f"💰 Calculating position sizes for {len(long_candidates)} longs and {len(short_candidates)} shorts...")

            if not long_candidates and not short_candidates:
                self.logger.warning("⚠️ No candidates for position sizing")
                return []

            all_candidates = long_candidates + short_candidates
            target_positions = []

            # Step 1: Calculate sigmoid weights
            sigmoid_weights = self._calculate_sigmoid_weights(all_candidates)

            # Step 2: Apply equal capital allocation
            equal_capital_weights = self._apply_equal_capital_allocation(sigmoid_weights, all_candidates)

            # Step 3: Apply volatility targeting
            vol_adjusted_weights = self._apply_volatility_targeting(all_candidates, equal_capital_weights)

            # Step 4: Create StrategyPosition objects with contract compliance and price validation
            target_positions = await self._create_positions_with_validation(
                all_candidates, vol_adjusted_weights, sigmoid_weights, long_candidates
            )

            # Beta projection is disabled by default for time-series strategies
            # Can be optionally enabled in config if portfolio-level neutrality is desired
            if self.config.get('enable_beta_projection', False):
                target_positions = await self._apply_beta_projection(target_positions)

            total_capital_allocated = sum(pos.size_usd for pos in target_positions)

            self.logger.info(f"✅ Position sizing complete:")
            self.logger.info(f"   Total positions: {len(target_positions)}")
            self.logger.info(f"   Total capital allocated: ${total_capital_allocated:,.0f}")
            self.logger.info(f"   Capital utilization: {total_capital_allocated/self.total_capital:.1%}")

            return target_positions

        except Exception as e:
            self.logger.error(f"❌ Failed to calculate position sizes: {e}")
            raise

    def _calculate_sigmoid_weights(self, candidates: List[Dict]) -> List[float]:
        """
        Apply sigmoid weighting function to trend signals

        Sigmoid function: tanh(2*trend_signal)
        This converts trend signals to weights between -1 and +1

        Args:
            candidates: List of candidate symbols with trend signals

        Returns:
            List of sigmoid weights
        """
        try:
            sigmoid_multiplier = self.config.get('sigmoid_multiplier', 2.0)
            sigmoid_weights = []

            for candidate in candidates:
                trend_signal = candidate.get('trend_signal', 0.0)

                # Apply sigmoid function: tanh(2*trend_signal)
                sigmoid_weight = math.tanh(sigmoid_multiplier * trend_signal)
                sigmoid_weights.append(sigmoid_weight)

            if self.config.get('log_sigmoid_calculations', False):
                self.logger.info("📊 Sigmoid weights calculated:")
                for i, candidate in enumerate(candidates):
                    self.logger.info(f"   {candidate['symbol']}: trend={candidate['trend_signal']:.4f} -> sigmoid={sigmoid_weights[i]:.4f}")

            return sigmoid_weights

        except Exception as e:
            self.logger.error(f"❌ Error calculating sigmoid weights: {e}")
            # Fallback to trend signals directly
            return [candidate.get('trend_signal', 0.0) for candidate in candidates]

    def _apply_equal_capital_allocation(self, sigmoid_weights: List[float], candidates: List[Dict]) -> List[float]:
        """
        Apply equal capital allocation to sigmoid weights

        Each coin gets 1/N of total capital before volatility targeting
        For universe of 50 coins: 1/50 = 2% per coin

        Args:
            sigmoid_weights: Sigmoid weights from trend signals
            candidates: List of candidate symbols

        Returns:
            List of equal capital allocated weights
        """
        try:
            universe_size = self.config.get('universe_size_for_allocation', 50)
            equal_allocation_factor = 1.0 / universe_size

            # Apply equal capital allocation
            equal_capital_weights = [weight * equal_allocation_factor for weight in sigmoid_weights]

            self.logger.debug(f"📊 Equal capital allocation applied: 1/{universe_size} = {equal_allocation_factor:.4f} per coin for {len(candidates)} candidates")

            return equal_capital_weights

        except Exception as e:
            self.logger.error(f"❌ Error applying equal capital allocation: {e}")
            return sigmoid_weights  # Fallback to sigmoid weights

    def _apply_volatility_targeting(self, candidates: List[Dict], base_weights: List[float]) -> List[float]:
        """Apply volatility targeting to adjust weights"""
        try:
            if len(candidates) != len(base_weights):
                raise ValueError("Candidates and weights length mismatch")

            target_volatility = self.config.get('target_volatility', 0.20)
            vol_adjusted_weights = []

            for i, candidate in enumerate(candidates):
                base_weight = base_weights[i]
                asset_volatility = candidate.get('weighted_volatility', self.config.get('default_volatility', 0.20))

                # Avoid division by zero
                if asset_volatility <= 0:
                    asset_volatility = self.config.get('default_volatility', 0.20)

                # Calculate leverage adjustment: target_vol / asset_vol
                leverage_adjustment = target_volatility / asset_volatility

                # Apply adjustment to base weight
                adjusted_weight = base_weight * leverage_adjustment
                vol_adjusted_weights.append(adjusted_weight)

            return vol_adjusted_weights

        except Exception as e:
            self.logger.error(f"❌ Error applying volatility targeting: {e}")
            return base_weights  # Fallback to base weights

    async def _apply_beta_projection(self, positions: List[StrategyPosition]) -> List[StrategyPosition]:
        """Apply beta projection for portfolio beta neutrality (optional for time-series strategies)"""
        try:
            if not self.config.get('enable_beta_projection', False):
                return positions

            self.logger.info("🎯 Applying beta projection for portfolio neutrality...")

            # Import beta optimizer
            from execution.beta_optimizer import BetaOptimizer

            # Create beta optimizer with strategy config
            beta_config = {
                'enable_beta_projection': True,
                'beta_neutrality_tolerance': self.config.get('beta_neutrality_tolerance', 0.05),
                'beta_optimization_max_weight_change': self.config.get('beta_optimization_max_weight_change', 0.20)
            }

            beta_optimizer = BetaOptimizer(beta_config)

            # Apply beta optimization
            optimized_positions = await beta_optimizer.optimize_for_beta_neutrality(
                positions, self.data_analyzer.beta_calculator
            )

            if optimized_positions:
                self.logger.info("✅ Beta projection applied successfully")
                return optimized_positions
            else:
                self.logger.warning("⚠️ Beta projection failed, using original positions")
                return positions

        except Exception as e:
            self.logger.warning(f"⚠️ Beta projection failed: {e}, using original positions")
            return positions

    async def _create_positions_with_validation(self, all_candidates: List[Dict], vol_adjusted_weights: List[float],
                                              sigmoid_weights: List[float], long_candidates: List[Dict]) -> List[StrategyPosition]:
        """
        Create positions with contract compliance and price validation
        """
        try:
            # Import contract spec manager for validation
            from utils.contract_specs import contract_spec_manager

            target_positions = []

            for i, candidate in enumerate(all_candidates):
                weight = vol_adjusted_weights[i]
                side = 'long' if candidate in long_candidates else 'short'

                # Calculate position size in USD
                position_size_usd = abs(weight) * self.total_capital

                # Get current price for position sizing
                price = float(candidate.get('price', candidate.get('last_price', 0)))
                if price <= 0:
                    self.logger.warning(f"⚠️ Invalid price for {candidate['symbol']}: {price}")
                    continue

                # Contract compliance: Calculate position size with rounding
                size_native, actual_position_usd = contract_spec_manager.calculate_position_size_with_rounding(
                    candidate['symbol'], position_size_usd, price)

                # Validate position size calculations
                if not DataUnitValidator.validate_position_size(candidate['symbol'], actual_position_usd, size_native, price):
                    self.logger.error(f"❌ Position size validation failed for {candidate['symbol']}")
                    continue

                # Round price for validation
                rounded_price = contract_spec_manager.round_price(candidate['symbol'], price, round_up=False)

                # Validate contract specifications
                is_valid, error_msg = contract_spec_manager.validate_order_specs(
                    candidate['symbol'], size_native, rounded_price)

                if not is_valid:
                    self.logger.error(f"❌ Contract spec validation failed for {candidate['symbol']}: {error_msg}")
                    continue

                target_positions.append(StrategyPosition(
                    symbol=candidate['symbol'],
                    side=side,
                    size_usd=actual_position_usd,
                    size_native=size_native,
                    weight=abs(weight),  # Store absolute weight for position sizing
                    metadata={
                        'trend_signal': candidate['trend_signal'],
                        'weighted_volatility': candidate.get('weighted_volatility', 0.2),
                        'sigmoid_weight': sigmoid_weights[i],
                        'vol_adjusted_weight': vol_adjusted_weights[i],
                        'strategy_source': 'trend_trading',
                        'strategy_type': 'time_series'
                    }
                ))

            return target_positions

        except Exception as e:
            self.logger.error(f"❌ Failed to create positions with validation: {e}")
            return []

    def get_strategy_info(self) -> Dict[str, Any]:
        """Get detailed information about this strategy"""
        base_info = super().get_strategy_info()

        strategy_info = {
            **base_info,
            'description': 'Trend Trading Strategy',
            'strategy_type': 'time_series_trend_following',
            'universe_selection': 'top_50_market_cap_with_volume_volatility_filters',
            'feature_engineering': 'ema_momentum_signals_multiple_timeframes',
            'position_selection': 'time_series_individual_evaluation',
            'position_sizing': 'sigmoid_weighting_with_equal_capital_allocation_and_volatility_targeting',
            'risk_management': 'volatility_targeting_and_buffer_zones',
            'exchange': self.exchange_name,
            'total_capital': self.total_capital,
            'key_parameters': {
                'top_market_cap_count': self.config.get('top_market_cap_count', 50),
                'target_volatility': self.config.get('target_volatility', 0.20),
                'min_volatility_threshold': self.config.get('min_volatility_threshold', 0.05),
                'sigmoid_multiplier': self.config.get('sigmoid_multiplier', 2.0),
                'universe_size_for_allocation': self.config.get('universe_size_for_allocation', 50),
                'ema_timeframes': self.config.get('ema_timeframes', []),
                'enable_beta_projection': self.config.get('enable_beta_projection', False),
                'rebalancing_frequency': self.config.get('rebalancing_frequency', 'daily'),
                'time_series_strategy': self.config.get('time_series_strategy', True)
            },
            'filters': {
                'min_daily_volume_usd': self.config.get('min_daily_volume_usd', 3000000),
                'exclude_stablecoins': self.config.get('exclude_stablecoins', True),
                'exclude_wrapped_coins': self.config.get('exclude_wrapped_coins', True),
                'min_historical_data_days': self.config.get('min_historical_data_days', 150)
            },
            'sizing_methodology': {
                'step_1': 'sigmoid_weighting_tanh_2x_trend_signal',
                'step_2': 'equal_capital_allocation_1_over_N',
                'step_3': 'volatility_targeting_target_vol_over_asset_vol',
                'step_4': 'contract_compliance_and_price_validation'
            }
        }

        return strategy_info
