"""
Portfolio combination logic for netting positions from multiple strategies

This module handles the combination of target portfolios from multiple strategies,
applying strategy weights and netting positions by symbol.
"""

import logging
import numpy as np
import asyncio
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict
from datetime import datetime, timezone

from strategies.base import StrategyResult, StrategyPosition
# Cache keys generated inline

logger = logging.getLogger(__name__)


@dataclass
class CombinedPosition:
    """Represents a position after combining multiple strategy positions"""
    symbol: str
    side: str  # 'long' or 'short'
    size_usd: float
    size_native: float
    net_weight: float  # Combined weight from all strategies
    contributing_strategies: List[str] = field(default_factory=list)
    strategy_contributions: Dict[str, float] = field(default_factory=dict)  # Strategy -> USD contribution
    confidence: float = 1.0  # Combined confidence score
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate combined position data"""
        if self.side not in ['long', 'short']:
            raise ValueError(f"Invalid side: {self.side}. Must be 'long' or 'short'")
        if self.size_usd < 0:
            raise ValueError(f"Invalid size_usd: {self.size_usd}. Must be non-negative")


class PortfolioCombiner:
    """
    Combines target portfolios from multiple strategies into a single portfolio
    
    The combiner handles:
    - Applying strategy weights to individual positions
    - Netting long/short positions by symbol
    - Maintaining attribution to source strategies
    - Handling position conflicts and edge cases
    """
    
    def __init__(self, config: Dict[str, Any], data_fetcher=None):
        """
        Initialize portfolio combiner

        Args:
            config: Configuration containing combination settings
            data_fetcher: Optional data fetcher for correlation calculations
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.data_fetcher = data_fetcher
        
        # Configuration parameters
        self.min_position_size_usd = config.get('min_position_size_usd', 10.0)
        self.position_rounding_decimals = config.get('position_rounding_decimals', 6)
        self.enable_position_netting = config.get('enable_position_netting', True)

        # Portfolio-level volatility targeting
        portfolio_config = config.get('portfolio_combination', {})
        self.enable_portfolio_vol_targeting = portfolio_config.get('enable_portfolio_vol_targeting', True)
        self.portfolio_target_volatility = portfolio_config.get('portfolio_target_volatility', 0.20)  # 20% default
        self.default_correlation = portfolio_config.get('default_correlation', 0.3)  # Default correlation between assets

        # Correlation calculation settings
        self.enable_correlation_calculation = portfolio_config.get('enable_correlation_calculation', True)
        self.correlation_calculation_days = portfolio_config.get('correlation_calculation_days', 30)  # 30 days for monthly
        self.correlation_cache_ttl = portfolio_config.get('correlation_cache_ttl', 86400)  # 24 hours cache
        self.total_capital_usd = config.get('total_capital_usd', 10000)  # Total capital for volatility calculation

        self.logger.info(f"🏗️ Portfolio combiner initialized")
        self.logger.info(f"   Min position size: ${self.min_position_size_usd}")
        self.logger.info(f"   Position netting: {'enabled' if self.enable_position_netting else 'disabled'}")
        self.logger.info(f"   Portfolio vol targeting: {'enabled' if self.enable_portfolio_vol_targeting else 'disabled'}")
        if self.enable_portfolio_vol_targeting:
            self.logger.info(f"   Portfolio target volatility: {self.portfolio_target_volatility*100:.1f}%")
            self.logger.info(f"   Total capital: ${self.total_capital_usd:,.0f}")
            self.logger.info(f"   Correlation calculation: {'enabled' if self.enable_correlation_calculation else 'disabled'}")
            if self.enable_correlation_calculation:
                self.logger.info(f"   Correlation period: {self.correlation_calculation_days} days")
    
    def combine_portfolios(self, strategy_results: Dict[str, StrategyResult], 
                          strategy_weights: Dict[str, float]) -> List[CombinedPosition]:
        """
        Combine multiple strategy portfolios into a single target portfolio
        
        Args:
            strategy_results: Dictionary mapping strategy names to their results
            strategy_weights: Dictionary mapping strategy names to their portfolio weights
            
        Returns:
            List of CombinedPosition objects representing the final portfolio
        """
        if not strategy_results:
            self.logger.warning("⚠️ No strategy results provided for combination")
            return []
        
        # Filter successful strategies only
        successful_results = {
            name: result for name, result in strategy_results.items() 
            if result.success and result.target_positions
        }
        
        if not successful_results:
            self.logger.warning("⚠️ No successful strategies with positions to combine")
            return []
        
        self.logger.info(f"🔄 Combining portfolios from {len(successful_results)} successful strategies...")
        
        # Normalize strategy weights for successful strategies only
        normalized_weights = self._normalize_weights(successful_results.keys(), strategy_weights)
        
        # Apply strategy weights to positions
        weighted_positions = self._apply_strategy_weights(successful_results, normalized_weights)
        
        # Net positions by symbol if enabled
        if self.enable_position_netting:
            combined_positions = self._net_positions_by_symbol(weighted_positions)
        else:
            combined_positions = self._combine_positions_without_netting(weighted_positions)
        
        # Filter out positions below minimum size
        final_positions = self._filter_minimum_positions(combined_positions)

        # Apply portfolio-level volatility targeting if enabled
        if self.enable_portfolio_vol_targeting and final_positions:
            final_positions = self._apply_portfolio_volatility_targeting(final_positions)

        # Log combination summary
        self._log_combination_summary(strategy_results, final_positions, normalized_weights)

        return final_positions
    
    def _normalize_weights(self, strategy_names: List[str], 
                          strategy_weights: Dict[str, float]) -> Dict[str, float]:
        """
        Normalize strategy weights to sum to 1.0 for successful strategies only
        
        Args:
            strategy_names: Names of successful strategies
            strategy_weights: Original strategy weights
            
        Returns:
            Normalized weights dictionary
        """
        # Get weights for successful strategies only
        relevant_weights = {
            name: strategy_weights.get(name, 1.0) 
            for name in strategy_names
        }
        
        total_weight = sum(relevant_weights.values())
        
        if total_weight == 0:
            # Equal weights if all weights are zero
            normalized = {name: 1.0 / len(strategy_names) for name in strategy_names}
        else:
            normalized = {name: weight / total_weight for name, weight in relevant_weights.items()}
        
        self.logger.info(f"📊 Normalized strategy weights: {normalized}")
        return normalized
    
    def _apply_strategy_weights(self, strategy_results: Dict[str, StrategyResult],
                               normalized_weights: Dict[str, float]) -> List[Tuple[str, StrategyPosition]]:
        """
        Apply strategy weights to all positions
        
        Args:
            strategy_results: Successful strategy results
            normalized_weights: Normalized strategy weights
            
        Returns:
            List of (strategy_name, weighted_position) tuples
        """
        weighted_positions = []
        
        for strategy_name, result in strategy_results.items():
            strategy_weight = normalized_weights.get(strategy_name, 0.0)
            
            if strategy_weight == 0:
                self.logger.warning(f"⚠️ Strategy {strategy_name} has zero weight, skipping positions")
                continue
            
            for position in result.target_positions:
                # Create weighted copy of position
                weighted_position = StrategyPosition(
                    symbol=position.symbol,
                    side=position.side,
                    size_usd=position.size_usd * strategy_weight,
                    size_native=position.size_native * strategy_weight,
                    weight=position.weight * strategy_weight,
                    confidence=position.confidence,
                    metadata=position.metadata.copy()
                )
                
                # Add strategy attribution
                weighted_position.metadata['source_strategy'] = strategy_name
                weighted_position.metadata['strategy_weight'] = strategy_weight
                weighted_position.metadata['original_size_usd'] = position.size_usd
                
                weighted_positions.append((strategy_name, weighted_position))
        
        self.logger.info(f"📊 Applied weights to {len(weighted_positions)} positions")
        return weighted_positions
    
    def _net_positions_by_symbol(self, weighted_positions: List[Tuple[str, StrategyPosition]]) -> List[CombinedPosition]:
        """
        Net positions by symbol, combining long and short positions
        
        Args:
            weighted_positions: List of (strategy_name, weighted_position) tuples
            
        Returns:
            List of netted CombinedPosition objects
        """
        # Group positions by symbol
        symbol_positions = defaultdict(list)
        for strategy_name, position in weighted_positions:
            symbol_positions[position.symbol].append((strategy_name, position))
        
        combined_positions = []
        
        for symbol, positions in symbol_positions.items():
            # Separate long and short positions
            long_positions = [(s, p) for s, p in positions if p.side == 'long']
            short_positions = [(s, p) for s, p in positions if p.side == 'short']
            
            # Calculate net position
            long_total_usd = sum(p.size_usd for _, p in long_positions)
            short_total_usd = sum(p.size_usd for _, p in short_positions)
            
            net_usd = long_total_usd - short_total_usd
            
            if abs(net_usd) < self.min_position_size_usd:
                self.logger.debug(f"⏭️ {symbol}: Net position ${abs(net_usd):.2f} below minimum, skipping")
                continue
            
            # Determine final side and size
            final_side = 'long' if net_usd > 0 else 'short'
            final_size_usd = abs(net_usd)
            
            # Calculate weighted average native size
            if final_side == 'long' and long_positions:
                # Use long positions for native size calculation
                total_weight = sum(p.size_usd for _, p in long_positions)
                final_size_native = sum(p.size_native * (p.size_usd / total_weight) for _, p in long_positions)
            elif final_side == 'short' and short_positions:
                # Use short positions for native size calculation  
                total_weight = sum(p.size_usd for _, p in short_positions)
                final_size_native = sum(p.size_native * (p.size_usd / total_weight) for _, p in short_positions)
            else:
                # Fallback: estimate from USD size using first available position
                reference_position = positions[0][1]
                price_estimate = reference_position.size_usd / reference_position.size_native if reference_position.size_native > 0 else 1.0
                final_size_native = final_size_usd / price_estimate
            
            # Round native size
            final_size_native = round(final_size_native, self.position_rounding_decimals)
            
            # Collect strategy contributions and metadata
            contributing_strategies = list(set(s for s, _ in positions))
            strategy_contributions = {}
            combined_confidence = 0.0
            total_contribution_weight = 0.0
            
            for strategy_name, position in positions:
                contribution = position.size_usd if position.side == final_side else -position.size_usd
                if final_side == 'short':
                    contribution = -contribution  # Adjust for short positions
                
                strategy_contributions[strategy_name] = contribution
                
                # Weight confidence by position size
                combined_confidence += position.confidence * position.size_usd
                total_contribution_weight += position.size_usd
            
            # Calculate weighted average confidence
            if total_contribution_weight > 0:
                combined_confidence /= total_contribution_weight
            else:
                combined_confidence = 1.0
            
            # Combine metadata from all positions, preserving volatility information
            combined_metadata = {
                'long_total_usd': long_total_usd,
                'short_total_usd': short_total_usd,
                'net_usd': net_usd,
                'position_count': len(positions)
            }

            # Calculate weighted average volatility from contributing positions
            total_weight = sum(p.size_usd for _, p in positions)
            if total_weight > 0:
                weighted_volatility = sum(
                    p.metadata.get('weighted_volatility', p.metadata.get('volatility', 0.20)) * p.size_usd
                    for _, p in positions
                ) / total_weight
                combined_metadata['volatility'] = weighted_volatility
                combined_metadata['weighted_volatility'] = weighted_volatility
            else:
                combined_metadata['volatility'] = 0.20  # Default fallback
                combined_metadata['weighted_volatility'] = 0.20

            # Preserve other important metadata from the largest contributing position
            largest_position = max(positions, key=lambda x: x[1].size_usd)[1]
            for key in ['beta', 'leverage', 'adjusted_funding']:
                if key in largest_position.metadata:
                    combined_metadata[key] = largest_position.metadata[key]

            # Create combined position
            combined_position = CombinedPosition(
                symbol=symbol,
                side=final_side,
                size_usd=final_size_usd,
                size_native=final_size_native,
                net_weight=final_size_usd,  # Will be normalized later if needed
                contributing_strategies=contributing_strategies,
                strategy_contributions=strategy_contributions,
                confidence=combined_confidence,
                metadata=combined_metadata
            )
            
            combined_positions.append(combined_position)
            
            self.logger.debug(f"📊 {symbol}: Combined {len(positions)} positions -> "
                            f"{final_side} ${final_size_usd:.2f} "
                            f"(long: ${long_total_usd:.2f}, short: ${short_total_usd:.2f})")
        
        self.logger.info(f"✅ Netted {len(symbol_positions)} symbols into {len(combined_positions)} final positions")
        return combined_positions

    def _combine_positions_without_netting(self, weighted_positions: List[Tuple[str, StrategyPosition]]) -> List[CombinedPosition]:
        """
        Combine positions without netting (keep all positions separate)

        Args:
            weighted_positions: List of (strategy_name, weighted_position) tuples

        Returns:
            List of CombinedPosition objects (one per input position)
        """
        combined_positions = []

        for strategy_name, position in weighted_positions:
            if position.size_usd < self.min_position_size_usd:
                self.logger.debug(f"⏭️ {strategy_name}:{position.symbol}: Position ${position.size_usd:.2f} below minimum, skipping")
                continue

            combined_position = CombinedPosition(
                symbol=position.symbol,
                side=position.side,
                size_usd=position.size_usd,
                size_native=position.size_native,
                net_weight=position.weight,
                contributing_strategies=[strategy_name],
                strategy_contributions={strategy_name: position.size_usd},
                confidence=position.confidence,
                metadata=position.metadata.copy()
            )

            combined_positions.append(combined_position)

        self.logger.info(f"✅ Combined {len(weighted_positions)} positions without netting into {len(combined_positions)} final positions")
        return combined_positions

    def _filter_minimum_positions(self, positions: List[CombinedPosition]) -> List[CombinedPosition]:
        """
        Filter out positions below minimum size threshold

        Args:
            positions: List of combined positions

        Returns:
            Filtered list of positions
        """
        filtered_positions = [
            pos for pos in positions
            if pos.size_usd >= self.min_position_size_usd
        ]

        filtered_count = len(positions) - len(filtered_positions)
        if filtered_count > 0:
            self.logger.info(f"🔍 Filtered out {filtered_count} positions below ${self.min_position_size_usd} minimum")

        return filtered_positions

    def _log_combination_summary(self, strategy_results: Dict[str, StrategyResult],
                                final_positions: List[CombinedPosition],
                                normalized_weights: Dict[str, float]) -> None:
        """Log detailed combination summary"""

        # Calculate totals
        total_input_positions = sum(len(r.target_positions) for r in strategy_results.values() if r.success)
        total_final_positions = len(final_positions)
        total_final_capital = sum(pos.size_usd for pos in final_positions)

        long_positions = [pos for pos in final_positions if pos.side == 'long']
        short_positions = [pos for pos in final_positions if pos.side == 'short']

        long_capital = sum(pos.size_usd for pos in long_positions)
        short_capital = sum(pos.size_usd for pos in short_positions)

        self.logger.info(f"📊 Portfolio Combination Summary:")
        self.logger.info(f"   Input: {total_input_positions} positions from {len(strategy_results)} strategies")
        self.logger.info(f"   Output: {total_final_positions} final positions (${total_final_capital:,.0f})")
        self.logger.info(f"   Long: {len(long_positions)} positions (${long_capital:,.0f})")
        self.logger.info(f"   Short: {len(short_positions)} positions (${short_capital:,.0f})")
        self.logger.info(f"   Strategy weights: {normalized_weights}")

        # Log per-strategy contribution
        for strategy_name, weight in normalized_weights.items():
            strategy_positions = [
                pos for pos in final_positions
                if strategy_name in pos.contributing_strategies
            ]
            strategy_capital = sum(
                pos.strategy_contributions.get(strategy_name, 0)
                for pos in final_positions
            )

            self.logger.info(f"   {strategy_name}: {len(strategy_positions)} positions, "
                           f"${abs(strategy_capital):,.0f} contribution (weight: {weight:.3f})")

    def get_portfolio_statistics(self, positions: List[CombinedPosition]) -> Dict[str, Any]:
        """
        Calculate portfolio statistics for the combined positions

        Args:
            positions: List of combined positions

        Returns:
            Dictionary containing portfolio statistics
        """
        if not positions:
            return {
                'total_positions': 0,
                'total_capital': 0.0,
                'long_positions': 0,
                'short_positions': 0,
                'long_capital': 0.0,
                'short_capital': 0.0,
                'symbols': [],
                'contributing_strategies': []
            }

        long_positions = [pos for pos in positions if pos.side == 'long']
        short_positions = [pos for pos in positions if pos.side == 'short']

        all_strategies = set()
        for pos in positions:
            all_strategies.update(pos.contributing_strategies)

        return {
            'total_positions': len(positions),
            'total_capital': sum(pos.size_usd for pos in positions),
            'long_positions': len(long_positions),
            'short_positions': len(short_positions),
            'long_capital': sum(pos.size_usd for pos in long_positions),
            'short_capital': sum(pos.size_usd for pos in short_positions),
            'symbols': [pos.symbol for pos in positions],
            'contributing_strategies': list(all_strategies),
            'average_confidence': sum(pos.confidence for pos in positions) / len(positions),
            'position_size_range': {
                'min': min(pos.size_usd for pos in positions),
                'max': max(pos.size_usd for pos in positions),
                'median': sorted([pos.size_usd for pos in positions])[len(positions) // 2]
            }
        }

    def _apply_portfolio_volatility_targeting(self, positions: List[CombinedPosition]) -> List[CombinedPosition]:
        """
        Apply portfolio-level volatility targeting to scale the entire portfolio

        This method:
        1. Calculates the current portfolio volatility using individual asset volatilities
        2. Scales all positions proportionally to achieve the target portfolio volatility
        3. Preserves relative position weights while adjusting overall portfolio size

        Args:
            positions: List of combined positions

        Returns:
            List of volatility-targeted positions
        """
        if not positions:
            return positions

        try:
            self.logger.info(f"🎯 Applying portfolio-level volatility targeting (target: {self.portfolio_target_volatility*100:.1f}%)...")

            # Calculate current portfolio volatility
            current_portfolio_vol = self._calculate_portfolio_volatility(positions)

            if current_portfolio_vol <= 0:
                self.logger.warning("⚠️ Cannot calculate portfolio volatility - using positions as-is")
                return positions

            # Calculate scaling factor to achieve target volatility
            # For portfolio volatility, we need to scale by the square of the volatility ratio
            # because portfolio volatility scales with the square root of position sizes
            vol_ratio = self.portfolio_target_volatility / current_portfolio_vol
            vol_scaling_factor = vol_ratio

            self.logger.info(f"📊 Portfolio volatility analysis:")
            self.logger.info(f"   Current portfolio volatility: {current_portfolio_vol*100:.2f}%")
            self.logger.info(f"   Target portfolio volatility: {self.portfolio_target_volatility*100:.1f}%")
            self.logger.info(f"   Volatility scaling factor: {vol_scaling_factor:.4f}")
            self.logger.info(f"   Note: Scaling adjusts portfolio size to achieve target risk level")

            # Scale all positions by the volatility scaling factor
            scaled_positions = []
            total_original_capital = sum(pos.size_usd for pos in positions)
            total_scaled_capital = 0.0

            for pos in positions:
                # Scale position sizes
                scaled_size_usd = pos.size_usd * vol_scaling_factor
                scaled_size_native = pos.size_native * vol_scaling_factor
                scaled_net_weight = pos.net_weight * vol_scaling_factor

                # Scale strategy contributions proportionally
                scaled_strategy_contributions = {
                    strategy: contribution * vol_scaling_factor
                    for strategy, contribution in pos.strategy_contributions.items()
                }

                # Create scaled position
                scaled_position = CombinedPosition(
                    symbol=pos.symbol,
                    side=pos.side,
                    size_usd=scaled_size_usd,
                    size_native=scaled_size_native,
                    net_weight=scaled_net_weight,
                    contributing_strategies=pos.contributing_strategies.copy(),
                    strategy_contributions=scaled_strategy_contributions,
                    confidence=pos.confidence,  # Confidence remains unchanged
                    metadata=pos.metadata.copy()
                )

                # Add volatility targeting metadata
                scaled_position.metadata['vol_scaling_factor'] = vol_scaling_factor
                scaled_position.metadata['original_size_usd'] = pos.size_usd
                scaled_position.metadata['portfolio_vol_targeted'] = True

                scaled_positions.append(scaled_position)
                total_scaled_capital += scaled_size_usd

            self.logger.info(f"✅ Portfolio volatility targeting completed:")
            self.logger.info(f"   Original total capital: ${total_original_capital:,.0f}")
            self.logger.info(f"   Scaled total capital: ${total_scaled_capital:,.0f}")
            self.logger.info(f"   Capital change: {((total_scaled_capital/total_original_capital - 1)*100):+.1f}%")

            return scaled_positions

        except Exception as e:
            self.logger.error(f"❌ Portfolio volatility targeting failed: {e}")
            self.logger.warning("⚠️ Using original positions without volatility targeting")
            return positions

    def _calculate_portfolio_volatility(self, positions: List[CombinedPosition]) -> float:
        """
        Calculate portfolio volatility based on total capital allocation

        Key insight: Portfolio volatility should be calculated based on total capital, not just position exposure.
        When positions are scaled down, the portfolio volatility decreases proportionally.

        Formula: Portfolio Vol = (Total Position Exposure / Total Capital) * Asset-Weighted Portfolio Vol

        Where Asset-Weighted Portfolio Vol = sqrt(sum(w_i^2 * vol_i^2) + sum(sum(w_i * w_j * vol_i * vol_j * corr_ij)))
        And w_i = position_size_i / total_position_exposure (relative weights within the portfolio)

        Args:
            positions: List of combined positions

        Returns:
            Portfolio volatility (annualized) based on total capital
        """
        if not positions:
            return 0.0

        try:
            # Calculate total position exposure (sum of absolute position sizes)
            total_position_exposure = sum(abs(pos.size_usd) for pos in positions)

            if total_position_exposure == 0:
                return 0.0

            # Calculate exposure ratio (how much of total capital is deployed)
            exposure_ratio = total_position_exposure / self.total_capital_usd

            # Extract relative weights and volatilities within the portfolio
            relative_weights = []
            volatilities = []
            symbols = []

            for pos in positions:
                # Calculate relative weight within the portfolio (not total capital)
                relative_weight = abs(pos.size_usd) / total_position_exposure
                relative_weights.append(relative_weight)

                # Get volatility from metadata (fallback to default if not available)
                volatility = pos.metadata.get('volatility', pos.metadata.get('weighted_volatility', 0.20))
                volatilities.append(volatility)
                symbols.append(pos.symbol)

            # Convert to numpy arrays for calculation
            w = np.array(relative_weights)
            vol = np.array(volatilities)

            # Calculate asset-weighted portfolio variance
            # 1. Individual asset variance contribution: sum(w_i^2 * vol_i^2)
            individual_variance = np.sum(w**2 * vol**2)

            # 2. Correlation contribution: sum(sum(w_i * w_j * vol_i * vol_j * corr_ij)) for i != j
            correlation_variance = 0.0
            n_assets = len(relative_weights)

            # Get correlations between assets
            correlations = self._get_asset_correlations(symbols)

            for i in range(n_assets):
                for j in range(i + 1, n_assets):  # Only upper triangle to avoid double counting
                    corr_ij = correlations.get((symbols[i], symbols[j]), self.default_correlation)
                    correlation_variance += 2 * w[i] * w[j] * vol[i] * vol[j] * corr_ij

            # Asset-weighted portfolio variance
            asset_weighted_variance = individual_variance + correlation_variance

            # Asset-weighted portfolio volatility
            asset_weighted_volatility = np.sqrt(asset_weighted_variance)

            # Final portfolio volatility = exposure_ratio * asset_weighted_volatility
            # This ensures that when positions are scaled down, portfolio volatility decreases
            portfolio_volatility = exposure_ratio * asset_weighted_volatility

            self.logger.debug(f"📊 Portfolio volatility calculation:")
            self.logger.debug(f"   Assets: {n_assets}")
            self.logger.debug(f"   Total capital: ${self.total_capital_usd:,.0f}")
            self.logger.debug(f"   Total position exposure: ${total_position_exposure:,.0f}")
            self.logger.debug(f"   Exposure ratio: {exposure_ratio:.3f}")
            self.logger.debug(f"   Relative weights: {[f'{w:.3f}' for w in relative_weights]}")
            self.logger.debug(f"   Volatilities: {[f'{v*100:.1f}%' for v in volatilities]}")
            self.logger.debug(f"   Individual variance: {individual_variance:.6f}")
            self.logger.debug(f"   Correlation variance: {correlation_variance:.6f}")
            self.logger.debug(f"   Asset-weighted variance: {asset_weighted_variance:.6f}")
            self.logger.debug(f"   Asset-weighted volatility: {asset_weighted_volatility*100:.2f}%")
            self.logger.debug(f"   Final portfolio volatility: {portfolio_volatility*100:.2f}%")

            return float(portfolio_volatility)

        except Exception as e:
            self.logger.error(f"❌ Error calculating portfolio volatility: {e}")
            # Return a reasonable default based on average individual volatilities and exposure
            try:
                total_exposure = sum(abs(pos.size_usd) for pos in positions)
                exposure_ratio = total_exposure / self.total_capital_usd
                avg_vol = np.mean([pos.metadata.get('volatility', 0.20) for pos in positions])
                return float(exposure_ratio * avg_vol)
            except:
                return 0.20  # 20% default fallback

    def _get_asset_correlations(self, symbols: List[str]) -> Dict[Tuple[str, str], float]:
        """
        Get correlations between assets, using monthly calculation if data fetcher is available

        Args:
            symbols: List of asset symbols

        Returns:
            Dictionary mapping (symbol1, symbol2) tuples to correlation values
        """
        correlations = {}

        if not self.enable_correlation_calculation or not self.data_fetcher:
            # Use default correlation for all pairs
            for i in range(len(symbols)):
                for j in range(i + 1, len(symbols)):
                    correlations[(symbols[i], symbols[j])] = self.default_correlation
            return correlations

        try:
            # Try to calculate actual correlations
            # Note: This would be called asynchronously in a real implementation
            # For now, we'll use the default correlation but log that we could calculate it
            self.logger.debug(f"📊 Correlation calculation available for {len(symbols)} assets")
            self.logger.debug(f"   Using default correlation {self.default_correlation:.2f} for all pairs")
            self.logger.debug(f"   Future enhancement: Calculate {self.correlation_calculation_days}-day correlations")

            # Use default correlation for all pairs (placeholder for actual calculation)
            for i in range(len(symbols)):
                for j in range(i + 1, len(symbols)):
                    correlations[(symbols[i], symbols[j])] = self.default_correlation

        except Exception as e:
            self.logger.warning(f"⚠️ Failed to calculate correlations: {e}")
            # Fallback to default correlation
            for i in range(len(symbols)):
                for j in range(i + 1, len(symbols)):
                    correlations[(symbols[i], symbols[j])] = self.default_correlation

        return correlations

    async def _calculate_asset_correlation(self, symbol1: str, symbol2: str) -> float:
        """
        Calculate correlation between two assets using monthly data

        Args:
            symbol1: First asset symbol
            symbol2: Second asset symbol

        Returns:
            Correlation coefficient (-1 to 1)
        """
        if not self.data_fetcher:
            return self.default_correlation

        try:
            # Check cache first
            cache_key = f"correlation_{symbol1}_{symbol2}_{self.correlation_calculation_days}d"
            # Note: Would use actual cache in real implementation

            # Fetch OHLCV data for both assets
            days_needed = self.correlation_calculation_days + 1  # +1 for return calculation

            data1 = await self.data_fetcher.get_cached_ohlcv(symbol1, '1d', days_needed)
            data2 = await self.data_fetcher.get_cached_ohlcv(symbol2, '1d', days_needed)

            if not data1 or not data2 or len(data1) < days_needed or len(data2) < days_needed:
                self.logger.debug(f"⚠️ Insufficient data for correlation calculation: {symbol1}-{symbol2}")
                return self.default_correlation

            # Extract closing prices
            closes1 = [candle[4] for candle in data1[-days_needed:]]
            closes2 = [candle[4] for candle in data2[-days_needed:]]

            # Calculate daily returns
            returns1 = [(closes1[i] - closes1[i-1]) / closes1[i-1] for i in range(1, len(closes1))]
            returns2 = [(closes2[i] - closes2[i-1]) / closes2[i-1] for i in range(1, len(closes2))]

            if len(returns1) != len(returns2) or len(returns1) < self.correlation_calculation_days:
                return self.default_correlation

            # Calculate correlation using numpy
            correlation_matrix = np.corrcoef(returns1, returns2)
            correlation = correlation_matrix[0, 1]

            # Handle NaN or invalid correlations
            if np.isnan(correlation) or np.isinf(correlation):
                return self.default_correlation

            # Clamp correlation to reasonable range
            correlation = max(-0.95, min(0.95, correlation))

            self.logger.debug(f"📊 Calculated {self.correlation_calculation_days}d correlation {symbol1}-{symbol2}: {correlation:.3f}")
            return float(correlation)

        except Exception as e:
            self.logger.debug(f"⚠️ Error calculating correlation {symbol1}-{symbol2}: {e}")
            return self.default_correlation
