"""
Mock exchange implementations for testing
"""

import asyncio
import random
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add src to path for imports
src_path = Path(__file__).parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from exchanges.base import ExchangeInterface


class MockExchange(ExchangeInterface):
    """Base mock exchange for testing"""
    
    def __init__(self, name: str = "mock"):
        self._name = name
        self._initialized = False
        self._markets = {}
        self._tickers = {}
        self._positions = []
        self._orders = {}
        self._funding_rates = {}
        self._balance = {"USDT": 10000.0}
        self._order_counter = 1
        
    @property
    def name(self) -> str:
        return self._name
    
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize mock exchange"""
        await asyncio.sleep(0.01)  # Simulate network delay
        self._initialized = True
        
        # Generate mock markets
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT", "DOTUSDT"]
        for symbol in symbols:
            self._markets[symbol] = {
                "id": symbol,
                "symbol": symbol,
                "base": symbol[:-4],
                "quote": "USDT",
                "active": True,
                "type": "swap",
                "spot": False,
                "margin": False,
                "future": False,
                "swap": True,
                "option": False,
                "contract": True,
                "contractSize": 1.0,
                "precision": {"amount": 8, "price": 2},
                "limits": {
                    "amount": {"min": 0.001, "max": 1000000},
                    "price": {"min": 0.01, "max": 1000000},
                    "cost": {"min": 1.0, "max": None}
                }
            }
        
        return True
    
    async def fetch_markets(self) -> Dict[str, Any]:
        """Fetch mock markets"""
        await asyncio.sleep(0.01)
        return self._markets
    
    async def fetch_tickers(self) -> Dict[str, Any]:
        """Fetch mock tickers for all symbols"""
        await asyncio.sleep(0.02)
        tickers = {}
        for symbol in self._markets.keys():
            tickers[symbol] = await self._generate_ticker(symbol)
        return tickers
    
    async def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """Fetch mock ticker for single symbol"""
        await asyncio.sleep(0.01)
        return await self._generate_ticker(symbol)
    
    async def _generate_ticker(self, symbol: str) -> Dict[str, Any]:
        """Generate realistic ticker data"""
        base_prices = {
            "BTCUSDT": 45000,
            "ETHUSDT": 3000,
            "ADAUSDT": 0.5,
            "SOLUSDT": 100,
            "DOTUSDT": 8
        }
        
        base_price = base_prices.get(symbol, 100)
        price_variation = random.uniform(-0.05, 0.05)
        current_price = base_price * (1 + price_variation)
        
        return {
            "symbol": symbol,
            "timestamp": int(time.time() * 1000),
            "datetime": datetime.utcnow().isoformat(),
            "high": current_price * 1.02,
            "low": current_price * 0.98,
            "bid": current_price * 0.9999,
            "ask": current_price * 1.0001,
            "last": current_price,
            "close": current_price,
            "baseVolume": random.uniform(1000, 10000),
            "quoteVolume": random.uniform(1000000, 100000000),
            "info": {}
        }
    
    async def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int) -> List[List]:
        """Generate mock OHLCV data"""
        await asyncio.sleep(0.02)
        
        base_prices = {
            "BTCUSDT": 45000,
            "ETHUSDT": 3000,
            "ADAUSDT": 0.5,
            "SOLUSDT": 100,
            "DOTUSDT": 8
        }
        
        base_price = base_prices.get(symbol, 100)
        ohlcv_data = []
        
        # Generate historical data
        now = datetime.utcnow()
        timeframe_minutes = {"1m": 1, "5m": 5, "15m": 15, "1h": 60, "4h": 240, "1d": 1440}
        interval = timeframe_minutes.get(timeframe, 60)
        
        for i in range(limit):
            timestamp = now - timedelta(minutes=interval * (limit - i))
            ts = int(timestamp.timestamp() * 1000)
            
            # Generate realistic price movement
            price_change = random.uniform(-0.02, 0.02)
            open_price = base_price * (1 + price_change)
            high_price = open_price * random.uniform(1.0, 1.015)
            low_price = open_price * random.uniform(0.985, 1.0)
            close_price = open_price * random.uniform(0.99, 1.01)
            volume = random.uniform(100, 1000)
            
            ohlcv_data.append([ts, open_price, high_price, low_price, close_price, volume])
            base_price = close_price  # Use close as next base
        
        return ohlcv_data
    
    async def fetch_funding_rate(self, symbol: str) -> Dict[str, Any]:
        """Generate mock funding rate"""
        await asyncio.sleep(0.01)
        
        # Generate realistic funding rates
        funding_rate = random.uniform(-0.001, 0.001)
        
        return {
            "symbol": symbol,
            "fundingRate": funding_rate,
            "fundingTimestamp": int(time.time() * 1000),
            "fundingDatetime": datetime.utcnow().isoformat(),
            "info": {}
        }
    
    async def fetch_positions(self) -> List[Dict[str, Any]]:
        """Fetch mock positions"""
        await asyncio.sleep(0.01)
        return self._positions.copy()
    
    async def fetch_balance(self) -> Dict[str, Any]:
        """Fetch mock balance"""
        await asyncio.sleep(0.01)
        return {
            "USDT": {
                "free": self._balance["USDT"],
                "used": 0.0,
                "total": self._balance["USDT"]
            }
        }
    
    async def fetch_order_book(self, symbol: str) -> Dict[str, Any]:
        """Generate mock order book"""
        await asyncio.sleep(0.01)
        
        ticker = await self._generate_ticker(symbol)
        mid_price = ticker["last"]
        
        # Generate realistic order book
        bids = []
        asks = []
        
        for i in range(10):
            bid_price = mid_price * (1 - (i + 1) * 0.0001)
            ask_price = mid_price * (1 + (i + 1) * 0.0001)
            size = random.uniform(0.1, 10.0)
            
            bids.append([bid_price, size])
            asks.append([ask_price, size])
        
        return {
            "symbol": symbol,
            "bids": bids,
            "asks": asks,
            "timestamp": int(time.time() * 1000),
            "datetime": datetime.utcnow().isoformat(),
            "nonce": None
        }
    
    async def create_limit_order(self, symbol: str, side: str, amount: float, price: float, params: Dict) -> Dict[str, Any]:
        """Create mock limit order"""
        await asyncio.sleep(0.02)
        
        order_id = str(self._order_counter)
        self._order_counter += 1
        
        order = {
            "id": order_id,
            "symbol": symbol,
            "type": "limit",
            "side": side,
            "amount": amount,
            "price": price,
            "status": "open",
            "filled": 0.0,
            "remaining": amount,
            "timestamp": int(time.time() * 1000),
            "datetime": datetime.utcnow().isoformat(),
            "info": params
        }
        
        self._orders[order_id] = order
        return order
    
    async def create_market_order(self, symbol: str, side: str, amount: float, params: Dict) -> Dict[str, Any]:
        """Create mock market order"""
        await asyncio.sleep(0.02)
        
        ticker = await self._generate_ticker(symbol)
        price = ticker["ask"] if side == "buy" else ticker["bid"]
        
        order_id = str(self._order_counter)
        self._order_counter += 1
        
        order = {
            "id": order_id,
            "symbol": symbol,
            "type": "market",
            "side": side,
            "amount": amount,
            "price": price,
            "status": "closed",
            "filled": amount,
            "remaining": 0.0,
            "timestamp": int(time.time() * 1000),
            "datetime": datetime.utcnow().isoformat(),
            "info": params
        }
        
        self._orders[order_id] = order
        return order
    
    async def cancel_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Cancel mock order"""
        await asyncio.sleep(0.01)

        if order_id in self._orders:
            self._orders[order_id]["status"] = "canceled"
            return self._orders[order_id]
        else:
            raise Exception(f"Order {order_id} not found")

    async def fetch_funding_rate_history(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Fetch mock funding rate history"""
        await asyncio.sleep(0.01)
        history = []
        for i in range(limit):
            history.append({
                "symbol": symbol,
                "fundingRate": random.uniform(-0.001, 0.001),
                "fundingTimestamp": int(time.time() * 1000) - i * 28800000,  # 8h intervals
                "info": {}
            })
        return history

    async def fetch_open_orders(self, symbol: str = None) -> List[Dict[str, Any]]:
        """Fetch mock open orders"""
        await asyncio.sleep(0.01)
        open_orders = []
        for order in self._orders.values():
            if order["status"] == "open":
                if symbol is None or order["symbol"] == symbol:
                    open_orders.append(order)
        return open_orders

    async def fetch_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Fetch mock order by ID"""
        await asyncio.sleep(0.01)
        if order_id in self._orders:
            return self._orders[order_id]
        else:
            raise Exception(f"Order {order_id} not found")


class MockBybitExchange(MockExchange):
    """Mock Bybit exchange with specific characteristics"""
    
    def __init__(self):
        super().__init__("bybit")
        
    async def fetch_funding_rate(self, symbol: str) -> Dict[str, Any]:
        """Bybit-specific funding rate format"""
        await asyncio.sleep(0.01)
        
        funding_rate = random.uniform(-0.001, 0.001)
        
        return {
            "symbol": symbol,
            "fundingRate": funding_rate,
            "fundingTimestamp": int(time.time() * 1000),
            "fundingDatetime": datetime.utcnow().isoformat(),
            "markPrice": random.uniform(40000, 50000),
            "indexPrice": random.uniform(40000, 50000),
            "info": {
                "symbol": symbol,
                "fundingRate": str(funding_rate),
                "fundingRateTimestamp": str(int(time.time() * 1000))
            }
        }


class MockBinanceExchange(MockExchange):
    """Mock Binance exchange with specific characteristics"""
    
    def __init__(self):
        super().__init__("binance")
        
    async def fetch_funding_rate(self, symbol: str) -> Dict[str, Any]:
        """Binance-specific funding rate format"""
        await asyncio.sleep(0.01)
        
        funding_rate = random.uniform(-0.001, 0.001)
        
        return {
            "symbol": symbol,
            "fundingRate": funding_rate,
            "fundingTimestamp": int(time.time() * 1000),
            "fundingDatetime": datetime.utcnow().isoformat(),
            "info": {
                "symbol": symbol,
                "fundingRate": str(funding_rate),
                "fundingTime": str(int(time.time() * 1000))
            }
        }
