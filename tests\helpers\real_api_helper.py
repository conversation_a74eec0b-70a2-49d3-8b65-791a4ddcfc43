"""
Real API test helper for testing with actual exchange APIs using demo/testnet
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List

# Add src to path for imports
src_path = Path(__file__).parent.parent.parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from config import load_config
from exchanges import ExchangeFactory
from utils import get_logger

logger = get_logger(__name__)


class RealAPITestHelper:
    """
    Helper class for testing with real exchange APIs using demo/testnet
    """
    
    def __init__(self, exchange_name: str = "bybit", use_demo: bool = True):
        self.exchange_name = exchange_name.lower()
        self.use_demo = use_demo
        self.exchange = None
        self.config = None
        
    async def setup(self) -> bool:
        """
        Setup real exchange connection for testing
        
        Returns:
            True if setup successful, False otherwise
        """
        try:
            # Load test configuration
            self.config = self._load_test_config()
            
            # Create exchange instance
            self.exchange = ExchangeFactory.create_exchange(self.exchange_name)
            
            # Initialize with demo/testnet settings
            init_success = await self.exchange.initialize(self.config)
            
            if init_success:
                logger.info(f"✅ Real API test setup successful for {self.exchange_name}")
                return True
            else:
                logger.error(f"❌ Failed to initialize {self.exchange_name} exchange")
                return False
                
        except Exception as e:
            logger.error(f"❌ Real API test setup failed: {e}")
            return False
    
    def _load_test_config(self) -> Dict[str, Any]:
        """Load configuration for testing"""
        # Try to load from config file first
        try:
            config = load_config("config.yaml")
            test_config = config.to_dict()
        except:
            # Fallback to minimal config
            test_config = {
                "exchange": self.exchange_name,
                "use_demo": self.use_demo,
                "use_testnet": True,
                "total_capital_usd": 1000,
                "simulation_mode": True
            }
        
        # Override with test-specific settings
        test_config.update({
            "exchange": self.exchange_name,
            "use_demo": self.use_demo,
            "use_testnet": True,
            "simulation_mode": True,  # Always use simulation for tests
        })
        
        # Add API credentials from environment if available
        if self.exchange_name == "bybit":
            test_config["api_key"] = os.getenv("BYBIT_API_KEY", "")
            test_config["api_secret"] = os.getenv("BYBIT_API_SECRET", "")
        elif self.exchange_name == "binance":
            test_config["api_key"] = os.getenv("BINANCE_API_KEY", "")
            test_config["api_secret"] = os.getenv("BINANCE_API_SECRET", "")
        elif self.exchange_name == "okx":
            test_config["api_key"] = os.getenv("OKX_API_KEY", "")
            test_config["api_secret"] = os.getenv("OKX_API_SECRET", "")
            test_config["passphrase"] = os.getenv("OKX_PASSPHRASE", "")
        elif self.exchange_name == "hyperliquid":
            test_config["wallet_address"] = os.getenv("HYPERLIQUID_WALLET_ADDRESS", "")
            test_config["private_key"] = os.getenv("HYPERLIQUID_PRIVATE_KEY", "")
        
        return test_config
    
    async def test_basic_connectivity(self) -> bool:
        """Test basic exchange connectivity"""
        try:
            markets = await self.exchange.fetch_markets()
            assert isinstance(markets, dict), "Markets should be a dictionary"
            assert len(markets) > 0, "Should have at least one market"
            
            logger.info(f"✅ Basic connectivity test passed - {len(markets)} markets found")
            return True
            
        except Exception as e:
            logger.error(f"❌ Basic connectivity test failed: {e}")
            return False
    
    async def test_market_data_fetching(self, symbols: Optional[List[str]] = None) -> bool:
        """Test market data fetching"""
        if symbols is None:
            symbols = ["BTCUSDT", "ETHUSDT"]
        
        try:
            # Test ticker fetching
            for symbol in symbols:
                ticker = await self.exchange.fetch_ticker(symbol)
                assert isinstance(ticker, dict), f"Ticker for {symbol} should be a dictionary"
                assert "last" in ticker, f"Ticker for {symbol} should have 'last' price"
                assert ticker["last"] > 0, f"Last price for {symbol} should be positive"
            
            # Test OHLCV fetching
            for symbol in symbols:
                ohlcv = await self.exchange.fetch_ohlcv(symbol, "1h", 10)
                assert isinstance(ohlcv, list), f"OHLCV for {symbol} should be a list"
                assert len(ohlcv) > 0, f"Should have OHLCV data for {symbol}"
                
                # Validate OHLCV structure
                for candle in ohlcv:
                    assert len(candle) >= 6, "OHLCV candle should have at least 6 elements"
                    assert all(isinstance(x, (int, float)) for x in candle), "All OHLCV values should be numeric"
            
            # Test funding rate fetching
            for symbol in symbols:
                try:
                    funding = await self.exchange.fetch_funding_rate(symbol)
                    assert isinstance(funding, dict), f"Funding rate for {symbol} should be a dictionary"
                    assert "fundingRate" in funding, f"Funding rate for {symbol} should have 'fundingRate'"
                except Exception as e:
                    logger.warning(f"Funding rate test failed for {symbol}: {e}")
            
            logger.info(f"✅ Market data fetching test passed for {symbols}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Market data fetching test failed: {e}")
            return False
    
    async def test_orderbook_fetching(self, symbols: Optional[List[str]] = None) -> bool:
        """Test orderbook fetching"""
        if symbols is None:
            symbols = ["BTCUSDT"]
        
        try:
            for symbol in symbols:
                orderbook = await self.exchange.fetch_order_book(symbol)
                assert isinstance(orderbook, dict), f"Orderbook for {symbol} should be a dictionary"
                assert "bids" in orderbook, f"Orderbook for {symbol} should have bids"
                assert "asks" in orderbook, f"Orderbook for {symbol} should have asks"
                assert len(orderbook["bids"]) > 0, f"Should have bids for {symbol}"
                assert len(orderbook["asks"]) > 0, f"Should have asks for {symbol}"
                
                # Validate bid/ask structure
                for bid in orderbook["bids"][:5]:  # Check first 5 levels
                    assert len(bid) >= 2, "Bid should have price and size"
                    assert bid[0] > 0, "Bid price should be positive"
                    assert bid[1] > 0, "Bid size should be positive"
                
                for ask in orderbook["asks"][:5]:  # Check first 5 levels
                    assert len(ask) >= 2, "Ask should have price and size"
                    assert ask[0] > 0, "Ask price should be positive"
                    assert ask[1] > 0, "Ask size should be positive"
                
                # Check spread
                best_bid = orderbook["bids"][0][0]
                best_ask = orderbook["asks"][0][0]
                assert best_ask > best_bid, f"Best ask should be higher than best bid for {symbol}"
            
            logger.info(f"✅ Orderbook fetching test passed for {symbols}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Orderbook fetching test failed: {e}")
            return False
    
    async def test_rate_limiting(self) -> bool:
        """Test that rate limiting is working properly"""
        try:
            import time
            
            # Make multiple rapid requests and measure timing
            start_time = time.time()
            requests = []
            
            for i in range(5):
                request = self.exchange.fetch_ticker("BTCUSDT")
                requests.append(request)
            
            # Execute all requests
            await asyncio.gather(*requests)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Should take at least some time due to rate limiting
            assert total_time > 0.5, f"Rate limiting may not be working - {total_time}s for 5 requests"
            
            logger.info(f"✅ Rate limiting test passed - {total_time:.2f}s for 5 requests")
            return True
            
        except Exception as e:
            logger.error(f"❌ Rate limiting test failed: {e}")
            return False
    
    async def test_error_handling(self) -> bool:
        """Test error handling with invalid requests"""
        try:
            # Test invalid symbol
            try:
                await self.exchange.fetch_ticker("INVALID_SYMBOL")
                logger.warning("Expected error for invalid symbol, but got success")
            except Exception:
                pass  # Expected to fail
            
            # Test invalid timeframe
            try:
                await self.exchange.fetch_ohlcv("BTCUSDT", "invalid_timeframe", 10)
                logger.warning("Expected error for invalid timeframe, but got success")
            except Exception:
                pass  # Expected to fail
            
            logger.info("✅ Error handling test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error handling test failed: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """Run all real API tests"""
        results = {}
        
        # Setup
        setup_success = await self.setup()
        results["setup"] = setup_success
        
        if not setup_success:
            logger.error("❌ Setup failed, skipping other tests")
            return results
        
        # Run tests
        results["connectivity"] = await self.test_basic_connectivity()
        results["market_data"] = await self.test_market_data_fetching()
        results["orderbook"] = await self.test_orderbook_fetching()
        results["rate_limiting"] = await self.test_rate_limiting()
        results["error_handling"] = await self.test_error_handling()
        
        # Summary
        passed = sum(results.values())
        total = len(results)
        
        logger.info(f"Real API tests completed: {passed}/{total} passed")
        
        return results
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.exchange and hasattr(self.exchange, 'close'):
            try:
                await self.exchange.close()
            except:
                pass


# Convenience function for running real API tests
async def run_real_api_tests(exchange_name: str = "bybit") -> bool:
    """
    Run real API tests for specified exchange
    
    Args:
        exchange_name: Exchange to test
    
    Returns:
        True if all tests passed, False otherwise
    """
    helper = RealAPITestHelper(exchange_name)
    
    try:
        results = await helper.run_all_tests()
        return all(results.values())
    finally:
        await helper.cleanup()


if __name__ == "__main__":
    # Run tests if called directly
    async def main():
        success = await run_real_api_tests("bybit")
        sys.exit(0 if success else 1)
    
    asyncio.run(main())
